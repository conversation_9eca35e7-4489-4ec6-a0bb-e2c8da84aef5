import { ref, computed, onMounted, onUnmounted } from 'vue'

// 断点定义
export const BREAKPOINTS = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
} as const

export type Breakpoint = keyof typeof BREAKPOINTS

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop'

// 布局模式
export type LayoutMode = 'single' | 'dual' | 'triple'

// 侧边栏状态
export type SidebarState = 'collapsed' | 'expanded' | 'hidden'

export interface LayoutConfig {
  showSidebar: boolean
  showPreview: boolean
  showQuickStart: boolean
  sidebarWidth: number
  previewWidth: number
  contentMaxWidth: number
}

export interface ResponsiveLayoutState {
  // 屏幕信息
  screenWidth: number
  screenHeight: number
  currentBreakpoint: Breakpoint
  deviceType: DeviceType
  
  // 布局状态
  layoutMode: LayoutMode
  sidebarState: SidebarState
  isPreviewVisible: boolean
  isQuickStartVisible: boolean
  
  // 移动端特定
  isMobileMenuOpen: boolean
  isDrawerOpen: boolean
  
  // 触摸支持
  isTouchDevice: boolean
  
  // 用户偏好
  prefersReducedMotion: boolean
  prefersHighContrast: boolean
  prefersDarkMode: boolean
}

export function useResponsiveLayout() {
  // 响应式状态
  const state = ref<ResponsiveLayoutState>({
    screenWidth: 0,
    screenHeight: 0,
    currentBreakpoint: 'xs',
    deviceType: 'mobile',
    layoutMode: 'single',
    sidebarState: 'collapsed',
    isPreviewVisible: false,
    isQuickStartVisible: false,
    isMobileMenuOpen: false,
    isDrawerOpen: false,
    isTouchDevice: false,
    prefersReducedMotion: false,
    prefersHighContrast: false,
    prefersDarkMode: false
  })

  // 计算属性
  const isMobile = computed(() => state.value.deviceType === 'mobile')
  const isTablet = computed(() => state.value.deviceType === 'tablet')
  const isDesktop = computed(() => state.value.deviceType === 'desktop')
  
  const canShowSidebar = computed(() => 
    state.value.screenWidth >= BREAKPOINTS.md
  )
  
  const canShowPreview = computed(() => 
    state.value.screenWidth >= BREAKPOINTS.lg
  )
  
  const canShowTripleLayout = computed(() => 
    state.value.screenWidth >= BREAKPOINTS.xl
  )

  // 布局配置
  const layoutConfig = computed<LayoutConfig>(() => {
    const { screenWidth, deviceType, layoutMode } = state.value
    
    if (deviceType === 'mobile') {
      return {
        showSidebar: false,
        showPreview: false,
        showQuickStart: false,
        sidebarWidth: 0,
        previewWidth: 0,
        contentMaxWidth: screenWidth
      }
    }
    
    if (deviceType === 'tablet') {
      return {
        showSidebar: layoutMode !== 'single',
        showPreview: false,
        showQuickStart: false,
        sidebarWidth: layoutMode === 'dual' ? 280 : 0,
        previewWidth: 0,
        contentMaxWidth: screenWidth - (layoutMode === 'dual' ? 280 : 0)
      }
    }
    
    // 桌面端
    const sidebarWidth = layoutMode === 'single' ? 0 : 280
    const previewWidth = layoutMode === 'triple' ? 400 : 0
    const contentWidth = screenWidth - sidebarWidth - previewWidth
    
    return {
      showSidebar: layoutMode !== 'single',
      showPreview: layoutMode === 'triple',
      showQuickStart: layoutMode === 'triple',
      sidebarWidth,
      previewWidth,
      contentMaxWidth: Math.min(contentWidth, 1200) // 限制最大宽度
    }
  })

  // CSS 类名
  const containerClasses = computed(() => ({
    'layout-mobile': isMobile.value,
    'layout-tablet': isTablet.value,
    'layout-desktop': isDesktop.value,
    'layout-single': state.value.layoutMode === 'single',
    'layout-dual': state.value.layoutMode === 'dual',
    'layout-triple': state.value.layoutMode === 'triple',
    'sidebar-collapsed': state.value.sidebarState === 'collapsed',
    'sidebar-expanded': state.value.sidebarState === 'expanded',
    'sidebar-hidden': state.value.sidebarState === 'hidden',
    'preview-visible': state.value.isPreviewVisible,
    'quickstart-visible': state.value.isQuickStartVisible,
    'mobile-menu-open': state.value.isMobileMenuOpen,
    'drawer-open': state.value.isDrawerOpen,
    'touch-device': state.value.isTouchDevice,
    'reduced-motion': state.value.prefersReducedMotion,
    'high-contrast': state.value.prefersHighContrast,
    'dark-mode': state.value.prefersDarkMode
  }))

  const chatAreaClasses = computed(() => {
    const { layoutMode, deviceType } = state.value
    
    if (deviceType === 'mobile') {
      return ['w-full', 'h-full']
    }
    
    if (layoutMode === 'single') {
      return ['w-full', 'max-w-4xl', 'mx-auto']
    }
    
    if (layoutMode === 'dual') {
      return ['flex-1', 'min-w-0']
    }
    
    // triple layout
    return ['flex-1', 'min-w-0', 'max-w-3xl']
  })

  const sidebarClasses = computed(() => ({
    'w-0': !layoutConfig.value.showSidebar,
    'w-70': layoutConfig.value.showSidebar && isDesktop.value,
    'w-full': layoutConfig.value.showSidebar && isMobile.value,
    'fixed': isMobile.value,
    'relative': !isMobile.value,
    'z-50': isMobile.value,
    'transform': true,
    'transition-transform': !state.value.prefersReducedMotion,
    'duration-300': !state.value.prefersReducedMotion,
    'ease-in-out': !state.value.prefersReducedMotion,
    '-translate-x-full': isMobile.value && !state.value.isMobileMenuOpen,
    'translate-x-0': !isMobile.value || state.value.isMobileMenuOpen
  }))

  // 工具函数
  const updateScreenSize = () => {
    state.value.screenWidth = window.innerWidth
    state.value.screenHeight = window.innerHeight
    
    // 更新断点
    const width = state.value.screenWidth
    if (width >= BREAKPOINTS['2xl']) {
      state.value.currentBreakpoint = '2xl'
    } else if (width >= BREAKPOINTS.xl) {
      state.value.currentBreakpoint = 'xl'
    } else if (width >= BREAKPOINTS.lg) {
      state.value.currentBreakpoint = 'lg'
    } else if (width >= BREAKPOINTS.md) {
      state.value.currentBreakpoint = 'md'
    } else if (width >= BREAKPOINTS.sm) {
      state.value.currentBreakpoint = 'sm'
    } else {
      state.value.currentBreakpoint = 'xs'
    }
    
    // 更新设备类型
    if (width < BREAKPOINTS.md) {
      state.value.deviceType = 'mobile'
    } else if (width < BREAKPOINTS.lg) {
      state.value.deviceType = 'tablet'
    } else {
      state.value.deviceType = 'desktop'
    }
    
    // 自动调整布局模式
    if (state.value.deviceType === 'mobile') {
      state.value.layoutMode = 'single'
      state.value.sidebarState = 'hidden'
    } else if (state.value.deviceType === 'tablet') {
      if (state.value.layoutMode === 'triple') {
        state.value.layoutMode = 'dual'
      }
    }
  }

  const detectUserPreferences = () => {
    // 检测触摸设备
    state.value.isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0
    
    // 检测用户偏好
    if (window.matchMedia) {
      state.value.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches
      state.value.prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches
      state.value.prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
    }
  }

  // 布局控制方法
  const setLayoutMode = (mode: LayoutMode) => {
    if (mode === 'triple' && !canShowTripleLayout.value) {
      mode = 'dual'
    }
    if (mode === 'dual' && !canShowSidebar.value) {
      mode = 'single'
    }
    state.value.layoutMode = mode
  }

  const toggleSidebar = () => {
    if (isMobile.value) {
      state.value.isMobileMenuOpen = !state.value.isMobileMenuOpen
    } else {
      state.value.sidebarState = state.value.sidebarState === 'expanded' ? 'collapsed' : 'expanded'
    }
  }

  const togglePreview = () => {
    if (canShowPreview.value) {
      state.value.isPreviewVisible = !state.value.isPreviewVisible
    }
  }

  const toggleQuickStart = () => {
    if (canShowTripleLayout.value) {
      state.value.isQuickStartVisible = !state.value.isQuickStartVisible
    }
  }

  const closeMobileMenu = () => {
    state.value.isMobileMenuOpen = false
    state.value.isDrawerOpen = false
  }

  // 事件监听
  let resizeObserver: ResizeObserver | null = null

  const setupEventListeners = () => {
    // 窗口大小变化
    window.addEventListener('resize', updateScreenSize)
    
    // 媒体查询变化
    if (window.matchMedia) {
      const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
      const highContrastQuery = window.matchMedia('(prefers-contrast: high)')
      const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)')
      
      reducedMotionQuery.addEventListener('change', (e) => {
        state.value.prefersReducedMotion = e.matches
      })
      
      highContrastQuery.addEventListener('change', (e) => {
        state.value.prefersHighContrast = e.matches
      })
      
      darkModeQuery.addEventListener('change', (e) => {
        state.value.prefersDarkMode = e.matches
      })
    }
    
    // 使用 ResizeObserver 监听容器大小变化
    if (window.ResizeObserver) {
      resizeObserver = new ResizeObserver(updateScreenSize)
      resizeObserver.observe(document.documentElement)
    }
  }

  const removeEventListeners = () => {
    window.removeEventListener('resize', updateScreenSize)
    resizeObserver?.disconnect()
  }

  // 初始化
  const initialize = () => {
    updateScreenSize()
    detectUserPreferences()
    setupEventListeners()
  }

  // 生命周期
  onMounted(() => {
    initialize()
  })

  onUnmounted(() => {
    removeEventListeners()
  })

  return {
    // 状态
    state: readonly(state),
    
    // 计算属性
    isMobile,
    isTablet,
    isDesktop,
    canShowSidebar,
    canShowPreview,
    canShowTripleLayout,
    layoutConfig,
    containerClasses,
    chatAreaClasses,
    sidebarClasses,
    
    // 方法
    setLayoutMode,
    toggleSidebar,
    togglePreview,
    toggleQuickStart,
    closeMobileMenu,
    updateScreenSize,
    
    // 工具
    BREAKPOINTS
  }
}
