<template>
  <div class="improved-chat" :class="containerClasses">
    <!-- 移动端遮罩层 -->
    <div
      v-if="isMobile && (state.isMobileMenuOpen || state.isDrawerOpen)"
      class="mobile-overlay"
      @click="closeMobileMenu"
    ></div>

    <!-- 主布局容器 -->
    <div class="chat-layout" :class="layoutClasses">
      <!-- 侧边栏 -->
      <aside
        v-if="layoutConfig.showSidebar || isMobile"
        class="chat-sidebar"
        :class="sidebarClasses"
        :style="sidebarStyle"
      >
        <div class="sidebar-content">
          <!-- 侧边栏头部 -->
          <div class="sidebar-header">
            <h2 class="sidebar-title">聊天记录</h2>
            <button
              v-if="isMobile"
              @click="closeMobileMenu"
              class="close-sidebar-btn"
              aria-label="关闭侧边栏"
            >
              <CloseIcon />
            </button>
          </div>

          <!-- 聊天历史列表 -->
          <ChatHistory
            :conversations="conversations"
            :active-id="activeConversationId"
            @select="handleConversationSelect"
            @delete="handleConversationDelete"
            @rename="handleConversationRename"
          />
        </div>
      </aside>

      <!-- 主聊天区域 -->
      <main class="chat-main" :class="chatAreaClasses" :style="mainStyle">
        <!-- 聊天头部工具栏 -->
        <header class="chat-header">
          <div class="header-left">
            <button
              v-if="!layoutConfig.showSidebar"
              @click="toggleSidebar"
              class="sidebar-toggle-btn"
              :aria-label="isMobile ? '打开菜单' : '显示侧边栏'"
            >
              <MenuIcon />
            </button>

            <div class="conversation-info">
              <h1 class="conversation-title">{{ currentConversationTitle }}</h1>
              <div class="conversation-meta">
                <span class="model-info">{{ currentModelName }}</span>
                <span class="message-count">{{ messageCount }} 条消息</span>
              </div>
            </div>
          </div>

          <div class="header-right">
            <!-- 布局切换 -->
            <div v-if="isDesktop" class="layout-controls">
              <button
                @click="setLayoutMode('single')"
                :class="{ active: state.layoutMode === 'single' }"
                class="layout-btn"
                title="单栏布局"
              >
                <SingleLayoutIcon />
              </button>
              <button
                @click="setLayoutMode('dual')"
                :class="{ active: state.layoutMode === 'dual' }"
                class="layout-btn"
                title="双栏布局"
              >
                <DualLayoutIcon />
              </button>
              <button
                v-if="canShowTripleLayout"
                @click="setLayoutMode('triple')"
                :class="{ active: state.layoutMode === 'triple' }"
                class="layout-btn"
                title="三栏布局"
              >
                <TripleLayoutIcon />
              </button>
            </div>

            <!-- 其他操作 -->
            <div class="header-actions">
              <button
                @click="handleNewConversation"
                class="action-btn new-chat-btn"
                title="新建对话"
              >
                <PlusIcon />
              </button>

              <button
                v-if="canShowPreview"
                @click="togglePreview"
                :class="{ active: state.isPreviewVisible }"
                class="action-btn"
                title="代码预览"
              >
                <CodeIcon />
              </button>
            </div>
          </div>
        </header>

        <!-- 聊天内容区域 -->
        <div class="chat-content" ref="chatContentRef">
          <!-- 消息列表 -->
          <div class="messages-container" :class="messagesContainerClass">
            <!-- 欢迎界面 -->
            <WelcomeScreen
              v-if="!messages.length"
              :user-type="userType"
              :suggested-prompts="suggestedPrompts"
              @use-prompt="handleUsePrompt"
            />

            <!-- 消息列表 -->
            <div v-else class="messages-list">
              <EnhancedMessage
                v-for="message in messages"
                :key="message.id"
                :message-id="message.id"
                :content="message.content"
                :is-user="message.isUser"
                :sender-name="message.senderName"
                :avatar="message.avatar"
                :timestamp="message.timestamp"
                :is-loading="message.isLoading"
                :has-error="message.hasError"
                :error-message="message.errorMessage"
                :attachments="message.attachments"
                :is-markdown="message.isMarkdown"
                :can-regenerate="message.canRegenerate"
                :can-delete="message.canDelete"
                :show-feedback="message.showFeedback"
                :feedback="message.feedback"
                :special-type="message.specialType"
                :special-props="message.specialProps"
                @copy="handleMessageCopy"
                @regenerate="handleMessageRegenerate"
                @delete="handleMessageDelete"
                @retry="handleMessageRetry"
                @feedback="handleMessageFeedback"
                @image-preview="handleImagePreview"
                @special-action="handleSpecialAction"
              />
            </div>

            <!-- 滚动到底部按钮 -->
            <button
              v-if="showScrollToBottom"
              @click="scrollToBottom"
              class="scroll-to-bottom-btn"
              title="滚动到底部"
            >
              <ArrowDownIcon />
            </button>
          </div>
        </div>

        <!-- 输入区域 -->
        <footer class="chat-footer">
          <UnifiedInput
            v-model="inputMessage"
            :placeholder="inputPlaceholder"
            :disabled="isStreaming"
            :mode="inputMode"
            :current-model="currentModel"
            :is-streaming="isStreaming"
            :show-model-selector="showModelSelector"
            :show-style-selector="showStyleSelector"
            @send="handleSendMessage"
            @stop="handleStopGeneration"
            @model-change="handleModelChange"
            @file-upload="handleFileUpload"
          />
        </footer>
      </main>

      <!-- 代码预览区域 -->
      <aside
        v-if="layoutConfig.showPreview && state.isPreviewVisible"
        class="preview-sidebar"
        :style="previewStyle"
      >
        <CodePreview
          :code="previewCode"
          :language="previewLanguage"
          :title="previewTitle"
          @close="togglePreview"
          @copy="handleCodeCopy"
          @download="handleCodeDownload"
        />
      </aside>
    </div>

    <!-- 全局组件 -->
    <ImagePreviewModal
      v-if="imagePreview.visible"
      :image="imagePreview.image"
      @close="closeImagePreview"
    />

    <ConfirmDialog
      v-if="confirmDialog.visible"
      :title="confirmDialog.title"
      :message="confirmDialog.message"
      @confirm="confirmDialog.onConfirm"
      @cancel="confirmDialog.onCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useResponsiveLayout } from '@/composables/useResponsiveLayout'
import { useChatStore } from '@/store'
import { useBasicLayout } from '@/hooks/useBasicLayout'

// 组件导入
import UnifiedInput from '@/components/ChatInput/UnifiedInput.vue'
import EnhancedMessage from '@/components/ChatMessage/EnhancedMessage.vue'
import ChatHistory from '@/components/ChatHistory/index.vue'
import WelcomeScreen from '@/components/WelcomeScreen/index.vue'
import CodePreview from '@/components/CodePreview/index.vue'
import ImagePreviewModal from '@/components/ImagePreviewModal/index.vue'
import ConfirmDialog from '@/components/ConfirmDialog/index.vue'

// 图标组件
import CloseIcon from '@/components/icons/CloseIcon.vue'
import MenuIcon from '@/components/icons/MenuIcon.vue'
import PlusIcon from '@/components/icons/PlusIcon.vue'
import CodeIcon from '@/components/icons/CodeIcon.vue'
import ArrowDownIcon from '@/components/icons/ArrowDownIcon.vue'
import SingleLayoutIcon from '@/components/icons/SingleLayoutIcon.vue'
import DualLayoutIcon from '@/components/icons/DualLayoutIcon.vue'
import TripleLayoutIcon from '@/components/icons/TripleLayoutIcon.vue'

// 响应式布局
const {
  state,
  isMobile,
  isTablet,
  isDesktop,
  canShowSidebar,
  canShowPreview,
  canShowTripleLayout,
  layoutConfig,
  containerClasses,
  chatAreaClasses,
  sidebarClasses,
  setLayoutMode,
  toggleSidebar,
  togglePreview,
  closeMobileMenu
} = useResponsiveLayout()

// Store
const chatStore = useChatStore()

// 基础状态
const chatContentRef = ref<HTMLElement>()
const inputMessage = ref('')
const isStreaming = ref(false)
const showScrollToBottom = ref(false)

// 对话数据
const conversations = ref([])
const activeConversationId = ref('')
const messages = ref([])
const currentModel = ref(null)

// 预览相关
const previewCode = ref('')
const previewLanguage = ref('javascript')
const previewTitle = ref('')

// 模态框状态
const imagePreview = ref({
  visible: false,
  image: null
})

const confirmDialog = ref({
  visible: false,
  title: '',
  message: '',
  onConfirm: () => {},
  onCancel: () => {}
})

// 计算属性
const layoutClasses = computed(() => ({
  'single-layout': state.value.layoutMode === 'single',
  'dual-layout': state.value.layoutMode === 'dual',
  'triple-layout': state.value.layoutMode === 'triple'
}))

const sidebarStyle = computed(() => ({
  width: layoutConfig.value.showSidebar ? `${layoutConfig.value.sidebarWidth}px` : '0'
}))

const mainStyle = computed(() => {
  const { showSidebar, showPreview, sidebarWidth, previewWidth } = layoutConfig.value
  let marginLeft = 0
  let marginRight = 0

  if (showSidebar && !isMobile.value) {
    marginLeft = sidebarWidth
  }

  if (showPreview && state.value.isPreviewVisible) {
    marginRight = previewWidth
  }

  return {
    marginLeft: `${marginLeft}px`,
    marginRight: `${marginRight}px`
  }
})

const previewStyle = computed(() => ({
  width: `${layoutConfig.value.previewWidth}px`
}))

const messagesContainerClass = computed(() => ({
  'max-w-4xl': state.value.layoutMode === 'single',
  'max-w-full': state.value.layoutMode !== 'single',
  'mx-auto': state.value.layoutMode === 'single'
}))

const currentConversationTitle = computed(() => {
  // 获取当前对话标题
  return '新对话'
})

const currentModelName = computed(() => {
  return currentModel.value?.name || 'GPT-4'
})

const messageCount = computed(() => {
  return messages.value.length
})

const userType = computed(() => {
  // 根据路由或用户信息确定用户类型
  return 'student' // 或 'teacher'
})

const suggestedPrompts = computed(() => {
  return [
    '帮我写一个Vue组件',
    '解释一下JavaScript闭包',
    '如何优化网站性能？'
  ]
})

const inputPlaceholder = computed(() => {
  if (isStreaming.value) return '正在生成回复...'
  if (isMobile.value) return '输入消息...'
  return '输入消息，Enter发送，Shift+Enter换行'
})

const inputMode = computed(() => {
  if (!messages.value.length && userType.value === 'teacher') {
    return 'centered'
  }
  return 'default'
})

const showModelSelector = computed(() => {
  return isDesktop.value || !messages.value.length
})

const showStyleSelector = computed(() => {
  return currentModel.value?.type === 'image'
})

// 事件处理方法
const handleConversationSelect = (id: string) => {
  activeConversationId.value = id
  // 加载对话消息
}

const handleConversationDelete = (id: string) => {
  // 删除对话
}

const handleConversationRename = (id: string, newName: string) => {
  // 重命名对话
}

const handleNewConversation = () => {
  // 创建新对话
}

const handleUsePrompt = (prompt: string) => {
  inputMessage.value = prompt
  nextTick(() => {
    handleSendMessage({ message: prompt, files: [], model: currentModel.value, styles: [] })
  })
}

const handleSendMessage = async (data: any) => {
  // 发送消息逻辑
  isStreaming.value = true

  try {
    // 添加用户消息
    messages.value.push({
      id: Date.now().toString(),
      content: data.message,
      isUser: true,
      senderName: '我',
      timestamp: new Date(),
      attachments: data.files || []
    })

    // 清空输入
    inputMessage.value = ''

    // 滚动到底部
    await nextTick()
    scrollToBottom()

    // 模拟AI回复
    setTimeout(() => {
      messages.value.push({
        id: (Date.now() + 1).toString(),
        content: '这是一个模拟的AI回复。',
        isUser: false,
        senderName: currentModelName.value,
        timestamp: new Date(),
        avatar: currentModel.value?.avatar
      })

      isStreaming.value = false
      scrollToBottom()
    }, 2000)

  } catch (error) {
    console.error('发送消息失败:', error)
    isStreaming.value = false
  }
}

const handleStopGeneration = () => {
  isStreaming.value = false
}

const handleModelChange = (model: any) => {
  currentModel.value = model
}

const handleFileUpload = (files: File[]) => {
  // 处理文件上传
}

const handleMessageCopy = (content: string) => {
  navigator.clipboard.writeText(content)
}

const handleMessageRegenerate = () => {
  // 重新生成消息
}

const handleMessageDelete = () => {
  // 删除消息
}

const handleMessageRetry = () => {
  // 重试消息
}

const handleMessageFeedback = (type: 'like' | 'dislike') => {
  // 处理消息反馈
}

const handleImagePreview = (file: any) => {
  imagePreview.value = {
    visible: true,
    image: file
  }
}

const handleSpecialAction = (action: string, data: any) => {
  // 处理特殊操作
}

const closeImagePreview = () => {
  imagePreview.value.visible = false
}

const handleCodeCopy = (code: string) => {
  navigator.clipboard.writeText(code)
}

const handleCodeDownload = (code: string, filename: string) => {
  // 下载代码文件
}

const scrollToBottom = () => {
  if (chatContentRef.value) {
    chatContentRef.value.scrollTop = chatContentRef.value.scrollHeight
  }
}

// 滚动监听
const handleScroll = () => {
  if (!chatContentRef.value) return

  const { scrollTop, scrollHeight, clientHeight } = chatContentRef.value
  const isNearBottom = scrollHeight - scrollTop - clientHeight < 100

  showScrollToBottom.value = !isNearBottom && messages.value.length > 0
}

// 生命周期
onMounted(() => {
  if (chatContentRef.value) {
    chatContentRef.value.addEventListener('scroll', handleScroll)
  }
})

onUnmounted(() => {
  if (chatContentRef.value) {
    chatContentRef.value.removeEventListener('scroll', handleScroll)
  }
})
</script>

<style scoped>
/* 改进的聊天页面样式 */
.improved-chat {
  @apply h-full overflow-hidden bg-gray-50 dark:bg-gray-900;
}

/* 移动端遮罩层 */
.mobile-overlay {
  @apply fixed inset-0 bg-black/50 z-40 lg:hidden;
}

/* 主布局容器 */
.chat-layout {
  @apply h-full flex relative;
}

.single-layout {
  @apply flex-col lg:flex-row;
}

.dual-layout {
  @apply flex-row;
}

.triple-layout {
  @apply flex-row;
}

/* 侧边栏 */
.chat-sidebar {
  @apply bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700
         flex-shrink-0 transition-all duration-300 ease-in-out z-50;
}

.chat-sidebar.fixed {
  @apply fixed top-0 left-0 h-full;
}

.sidebar-content {
  @apply h-full flex flex-col;
}

.sidebar-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700;
}

.sidebar-title {
  @apply text-lg font-semibold text-gray-900 dark:text-gray-100;
}

.close-sidebar-btn {
  @apply p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700
         transition-colors duration-200 lg:hidden;
}

/* 主聊天区域 */
.chat-main {
  @apply flex-1 flex flex-col min-w-0 bg-white dark:bg-gray-800
         transition-all duration-300 ease-in-out;
}

/* 聊天头部 */
.chat-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700
         bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm sticky top-0 z-30;
}

.header-left {
  @apply flex items-center gap-3 flex-1 min-w-0;
}

.sidebar-toggle-btn {
  @apply p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700
         transition-colors duration-200 lg:hidden;
}

.conversation-info {
  @apply flex-1 min-w-0;
}

.conversation-title {
  @apply text-lg font-semibold text-gray-900 dark:text-gray-100 truncate;
}

.conversation-meta {
  @apply flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400;
}

.model-info {
  @apply px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300
         rounded-full text-xs font-medium;
}

.message-count {
  @apply text-xs;
}

.header-right {
  @apply flex items-center gap-3;
}

/* 布局控制 */
.layout-controls {
  @apply flex items-center gap-1 p-1 bg-gray-100 dark:bg-gray-700 rounded-lg;
}

.layout-btn {
  @apply p-2 rounded-md transition-all duration-200 text-gray-600 dark:text-gray-400
         hover:text-gray-900 dark:hover:text-gray-100 hover:bg-white dark:hover:bg-gray-600;
}

.layout-btn.active {
  @apply bg-white dark:bg-gray-600 text-blue-600 dark:text-blue-400 shadow-sm;
}

/* 头部操作 */
.header-actions {
  @apply flex items-center gap-2;
}

.action-btn {
  @apply p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700
         transition-colors duration-200 text-gray-600 dark:text-gray-400
         hover:text-gray-900 dark:hover:text-gray-100;
}

.action-btn.active {
  @apply bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400;
}

.new-chat-btn {
  @apply bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700
         text-white shadow-sm hover:shadow-md;
}

/* 聊天内容区域 */
.chat-content {
  @apply flex-1 overflow-y-auto scroll-smooth;
}

.messages-container {
  @apply p-4 space-y-4;
}

.messages-list {
  @apply space-y-4;
}

/* 滚动到底部按钮 */
.scroll-to-bottom-btn {
  @apply fixed bottom-24 right-6 p-3 bg-white dark:bg-gray-800
         border border-gray-200 dark:border-gray-700 rounded-full
         shadow-lg hover:shadow-xl transition-all duration-300
         text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400
         z-20 hover:scale-110;
}

/* 聊天底部 */
.chat-footer {
  @apply p-4 border-t border-gray-200 dark:border-gray-700
         bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm;
}

/* 代码预览侧边栏 */
.preview-sidebar {
  @apply bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700
         flex-shrink-0 transition-all duration-300 ease-in-out;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .chat-header {
    @apply px-3 py-3;
  }

  .conversation-title {
    @apply text-base;
  }

  .layout-controls {
    @apply hidden;
  }

  .messages-container {
    @apply px-3 py-3;
  }

  .chat-footer {
    @apply px-3 py-3;
  }

  .scroll-to-bottom-btn {
    @apply bottom-20 right-4 p-2;
  }
}

@media (max-width: 640px) {
  .header-actions {
    @apply gap-1;
  }

  .action-btn {
    @apply p-1.5;
  }

  .conversation-meta {
    @apply flex-col items-start gap-1;
  }
}

/* 平板端优化 */
@media (min-width: 768px) and (max-width: 1024px) {
  .chat-sidebar {
    @apply w-64;
  }

  .layout-controls {
    @apply hidden;
  }
}

/* 桌面端优化 */
@media (min-width: 1024px) {
  .chat-main {
    @apply rounded-lg shadow-sm;
  }

  .messages-container {
    @apply px-6 py-6;
  }

  .chat-footer {
    @apply px-6 py-4;
  }
}

/* 超宽屏优化 */
@media (min-width: 1536px) {
  .messages-container.max-w-4xl {
    @apply max-w-5xl;
  }
}

/* 无障碍优化 */
@media (prefers-reduced-motion: reduce) {
  .improved-chat * {
    @apply transition-none;
  }

  .scroll-to-bottom-btn {
    @apply transform-none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .chat-sidebar,
  .chat-main,
  .preview-sidebar {
    @apply border-2 border-gray-800 dark:border-gray-200;
  }

  .action-btn.active {
    @apply border-2 border-blue-800 dark:border-blue-200;
  }
}

/* 深色模式特定优化 */
.dark .chat-layout {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

.dark .chat-header,
.dark .chat-footer {
  @apply bg-gray-800/95 border-gray-700;
}

.dark .scroll-to-bottom-btn {
  @apply bg-gray-800 border-gray-600;
}

/* 焦点状态 */
.action-btn:focus,
.layout-btn:focus,
.sidebar-toggle-btn:focus,
.close-sidebar-btn:focus,
.scroll-to-bottom-btn:focus {
  @apply outline-none ring-2 ring-blue-500/50 ring-offset-2 ring-offset-white dark:ring-offset-gray-800;
}

/* 加载状态 */
.chat-main.loading {
  @apply pointer-events-none opacity-75;
}

/* 错误状态 */
.chat-main.error {
  @apply border-red-200 dark:border-red-800;
}

/* 自定义滚动条 */
.chat-content::-webkit-scrollbar {
  @apply w-2;
}

.chat-content::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

.chat-content::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

.chat-content::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}
</style>
