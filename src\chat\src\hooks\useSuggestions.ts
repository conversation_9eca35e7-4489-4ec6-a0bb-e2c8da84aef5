import { ref, computed } from 'vue'

export interface Suggestion {
  id: string
  name: string
  description?: string
  avatar?: string
  type: 'user' | 'model' | 'prompt' | 'command'
  data?: any
}

export function useSuggestions() {
  const suggestions = ref<Suggestion[]>([])
  const showSuggestions = ref(false)
  const selectedSuggestionIndex = ref(-1)
  const searchTerm = ref('')

  // 模拟的建议数据
  const mockSuggestions: Suggestion[] = [
    {
      id: 'gpt-4',
      name: 'GPT-4',
      description: '最强大的语言模型',
      type: 'model',
      avatar: '/avatars/gpt4.png'
    },
    {
      id: 'claude-3',
      name: 'Claude-<PERSON>',
      description: '擅长分析和推理',
      type: 'model',
      avatar: '/avatars/claude.png'
    },
    {
      id: 'gemini-pro',
      name: 'Gemini Pro',
      description: 'Google的多模态模型',
      type: 'model',
      avatar: '/avatars/gemini.png'
    },
    {
      id: 'help',
      name: '帮助',
      description: '显示可用命令',
      type: 'command'
    },
    {
      id: 'clear',
      name: '清空',
      description: '清空当前对话',
      type: 'command'
    },
    {
      id: 'export',
      name: '导出',
      description: '导出对话记录',
      type: 'command'
    }
  ]

  const filteredSuggestions = computed(() => {
    if (!searchTerm.value) return mockSuggestions.slice(0, 6)
    
    const term = searchTerm.value.toLowerCase()
    return mockSuggestions.filter(suggestion => 
      suggestion.name.toLowerCase().includes(term) ||
      suggestion.description?.toLowerCase().includes(term)
    ).slice(0, 6)
  })

  const searchSuggestions = (term: string) => {
    searchTerm.value = term
    suggestions.value = filteredSuggestions.value
    showSuggestions.value = term.length > 0 && suggestions.value.length > 0
    selectedSuggestionIndex.value = suggestions.value.length > 0 ? 0 : -1
  }

  const selectSuggestion = (suggestion: Suggestion) => {
    clearSuggestions()
    return suggestion
  }

  const clearSuggestions = () => {
    suggestions.value = []
    showSuggestions.value = false
    selectedSuggestionIndex.value = -1
    searchTerm.value = ''
  }

  const navigateSuggestions = (direction: 'up' | 'down') => {
    if (!showSuggestions.value || suggestions.value.length === 0) return

    if (direction === 'down') {
      selectedSuggestionIndex.value = Math.min(
        selectedSuggestionIndex.value + 1,
        suggestions.value.length - 1
      )
    } else {
      selectedSuggestionIndex.value = Math.max(
        selectedSuggestionIndex.value - 1,
        0
      )
    }
  }

  const getSelectedSuggestion = () => {
    if (selectedSuggestionIndex.value >= 0 && selectedSuggestionIndex.value < suggestions.value.length) {
      return suggestions.value[selectedSuggestionIndex.value]
    }
    return null
  }

  return {
    suggestions,
    showSuggestions,
    selectedSuggestionIndex,
    searchTerm,
    searchSuggestions,
    selectSuggestion,
    clearSuggestions,
    navigateSuggestions,
    getSelectedSuggestion
  }
}
