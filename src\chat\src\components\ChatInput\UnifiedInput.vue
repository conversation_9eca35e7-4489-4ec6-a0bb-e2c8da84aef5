<template>
  <div class="unified-input-container" :class="containerClass">
    <!-- 输入区域 -->
    <div class="input-wrapper" :class="wrapperClass">
      <!-- 文件预览区域 -->
      <div v-if="uploadedFiles.length > 0" class="file-preview-area">
        <div
          v-for="(file, index) in uploadedFiles"
          :key="index"
          class="file-preview-item"
        >
          <img v-if="file.type.startsWith('image/')" :src="file.preview" :alt="file.name" />
          <div v-else class="file-icon">
            <FileIcon :type="file.type" />
            <span class="file-name">{{ file.name }}</span>
          </div>
          <button @click="removeFile(index)" class="remove-file-btn" :aria-label="`删除文件 ${file.name}`">
            <CloseIcon />
          </button>
        </div>
      </div>

      <!-- 主输入区域 -->
      <div class="main-input-area">
        <!-- 工具栏 -->
        <div v-if="showToolbar" class="input-toolbar">
          <!-- 文件上传 -->
          <button
            @click="triggerFileUpload"
            class="toolbar-btn"
            :disabled="isUploading"
            :aria-label="isUploading ? '正在上传文件' : '上传文件'"
          >
            <LoadingIcon v-if="isUploading" class="animate-spin" />
            <AttachIcon v-else />
          </button>

          <!-- 模型选择 -->
          <ModelSelector
            v-if="showModelSelector"
            :current-model="currentModel"
            @change="handleModelChange"
          />

          <!-- 样式选择（图像模型） -->
          <StyleSelector
            v-if="showStyleSelector && isImageModel"
            :styles="availableStyles"
            @select="handleStyleSelect"
          />
        </div>

        <!-- 输入框 -->
        <div class="textarea-container">
          <textarea
            ref="textareaRef"
            v-model="inputValue"
            :placeholder="placeholder"
            :disabled="disabled"
            class="main-textarea"
            :style="textareaStyle"
            @input="handleInput"
            @keydown="handleKeydown"
            @paste="handlePaste"
            @focus="handleFocus"
            @blur="handleBlur"
            :aria-label="placeholder"
            :aria-describedby="errorMessage ? 'input-error' : undefined"
          />

          <!-- 字符计数 -->
          <div v-if="showCharCount" class="char-count">
            {{ inputValue.length }}{{ maxLength ? `/${maxLength}` : '' }}
          </div>
        </div>

        <!-- 发送按钮 -->
        <div class="send-button-area">
          <button
            v-if="!isStreaming"
            @click="handleSend"
            :disabled="!canSend"
            class="send-btn"
            :class="sendButtonClass"
            :aria-label="canSend ? '发送消息' : '请输入内容'"
          >
            <SendIcon />
          </button>

          <button
            v-else
            @click="handleStop"
            class="stop-btn"
            aria-label="停止生成"
          >
            <StopIcon />
          </button>
        </div>
      </div>

      <!-- 错误提示 -->
      <div v-if="errorMessage" id="input-error" class="error-message" role="alert">
        {{ errorMessage }}
      </div>

      <!-- 建议列表 -->
      <div v-if="showSuggestions && suggestions.length > 0" class="suggestions-list">
        <div
          v-for="(suggestion, index) in suggestions"
          :key="index"
          @click="selectSuggestion(suggestion)"
          class="suggestion-item"
          :class="{ active: selectedSuggestionIndex === index }"
          role="option"
          :aria-selected="selectedSuggestionIndex === index"
        >
          <img v-if="suggestion.avatar" :src="suggestion.avatar" :alt="suggestion.name" class="suggestion-avatar" />
          <div class="suggestion-content">
            <div class="suggestion-name">{{ suggestion.name }}</div>
            <div class="suggestion-desc">{{ suggestion.description }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      multiple
      :accept="acceptedFileTypes"
      @change="handleFileSelect"
      class="hidden"
      :aria-label="'选择要上传的文件'"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch, onMounted, onUnmounted } from 'vue'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { useFileUpload } from '@/hooks/useFileUpload'
import { useSuggestions } from '@/hooks/useSuggestions'

// 图标组件
import FileIcon from './icons/FileIcon.vue'
import CloseIcon from './icons/CloseIcon.vue'
import LoadingIcon from './icons/LoadingIcon.vue'
import AttachIcon from './icons/AttachIcon.vue'
import SendIcon from './icons/SendIcon.vue'
import StopIcon from './icons/StopIcon.vue'

// 子组件
import ModelSelector from './ModelSelector.vue'
import StyleSelector from './StyleSelector.vue'

interface Props {
  modelValue: string
  placeholder?: string
  disabled?: boolean
  mode?: 'default' | 'centered' | 'compact'
  showToolbar?: boolean
  showModelSelector?: boolean
  showStyleSelector?: boolean
  showCharCount?: boolean
  maxLength?: number
  minRows?: number
  maxRows?: number
  acceptedFileTypes?: string
  currentModel?: any
  isStreaming?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'send', data: { message: string; files: File[]; model: any; styles: string[] }): void
  (e: 'stop'): void
  (e: 'model-change', model: any): void
  (e: 'file-upload', files: File[]): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入消息...',
  disabled: false,
  mode: 'default',
  showToolbar: true,
  showModelSelector: true,
  showStyleSelector: true,
  showCharCount: false,
  minRows: 1,
  maxRows: 8,
  acceptedFileTypes: 'image/*,.pdf,.txt,.docx,.pptx,.xlsx,.xml,.js,.json,.sql',
  isStreaming: false
})

const emit = defineEmits<Emits>()

// 基础设置
const { isMobile } = useBasicLayout()
const textareaRef = ref<HTMLTextAreaElement>()
const fileInputRef = ref<HTMLInputElement>()

// 文件上传
const {
  uploadedFiles,
  isUploading,
  uploadFile,
  removeFile,
  clearFiles
} = useFileUpload()

// 建议系统
const {
  suggestions,
  showSuggestions,
  selectedSuggestionIndex,
  searchSuggestions,
  selectSuggestion,
  clearSuggestions
} = useSuggestions()

// 状态管理
const inputValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isFocused = ref(false)
const errorMessage = ref('')
const selectedStyles = ref<string[]>([])

// 计算属性
const containerClass = computed(() => ({
  'unified-input-container': true,
  'mode-centered': props.mode === 'centered',
  'mode-compact': props.mode === 'compact',
  'is-mobile': isMobile.value,
  'is-focused': isFocused.value,
  'has-error': !!errorMessage.value
}))

const wrapperClass = computed(() => ({
  'input-wrapper': true,
  'with-files': uploadedFiles.value.length > 0,
  'is-streaming': props.isStreaming
}))

const textareaStyle = computed(() => {
  const lineHeight = 24
  const minHeight = lineHeight * props.minRows + 16
  const maxHeight = lineHeight * props.maxRows + 16

  return {
    minHeight: `${minHeight}px`,
    maxHeight: `${maxHeight}px`
  }
})

const canSend = computed(() => {
  return !props.disabled &&
         !isUploading.value &&
         (inputValue.value.trim() || uploadedFiles.value.length > 0)
})

const sendButtonClass = computed(() => ({
  'send-btn-enabled': canSend.value,
  'send-btn-disabled': !canSend.value
}))

const isImageModel = computed(() => {
  return props.currentModel?.type === 'image' ||
         ['dall-e-3', 'midjourney', 'flux'].includes(props.currentModel?.name)
})

const availableStyles = computed(() => {
  // 根据当前模型返回可用样式
  return []
})

// 基础方法已通过 hooks 提供

// 事件处理
const handleInput = () => {
  nextTick(() => {
    autoResize()

    // 处理@符号建议
    if (inputValue.value.includes('@')) {
      const atIndex = inputValue.value.lastIndexOf('@')
      const searchTerm = inputValue.value.slice(atIndex + 1)
      searchSuggestions(searchTerm)
    } else {
      clearSuggestions()
    }
  })
}

const handleKeydown = (event: KeyboardEvent) => {
  // 处理建议选择
  if (showSuggestions.value && suggestions.value.length > 0) {
    if (event.key === 'ArrowDown') {
      event.preventDefault()
      selectedSuggestionIndex.value = Math.min(
        selectedSuggestionIndex.value + 1,
        suggestions.value.length - 1
      )
      return
    }

    if (event.key === 'ArrowUp') {
      event.preventDefault()
      selectedSuggestionIndex.value = Math.max(selectedSuggestionIndex.value - 1, 0)
      return
    }

    if (event.key === 'Enter' && selectedSuggestionIndex.value >= 0) {
      event.preventDefault()
      selectSuggestion(suggestions.value[selectedSuggestionIndex.value])
      return
    }

    if (event.key === 'Escape') {
      event.preventDefault()
      clearSuggestions()
      return
    }
  }

  // 发送消息
  if (event.key === 'Enter') {
    if (isMobile.value) {
      // 移动端：Ctrl+Enter 发送
      if (event.ctrlKey) {
        event.preventDefault()
        handleSend()
      }
    } else {
      // 桌面端：Enter 发送，Shift+Enter 换行
      if (!event.shiftKey) {
        event.preventDefault()
        handleSend()
      }
    }
  }
}

const handlePaste = async (event: ClipboardEvent) => {
  const items = event.clipboardData?.items
  if (!items) return

  for (const item of items) {
    if (item.type.startsWith('image/')) {
      event.preventDefault()
      const file = item.getAsFile()
      if (file) {
        await uploadFile(file)
      }
    }
  }

  nextTick(() => autoResize())
}

const handleFocus = () => {
  isFocused.value = true
  errorMessage.value = ''
}

const handleBlur = () => {
  isFocused.value = false
}

const handleSend = () => {
  if (!canSend.value) return

  const message = inputValue.value.trim()
  if (!message && uploadedFiles.value.length === 0) {
    errorMessage.value = '请输入消息内容或上传文件'
    return
  }

  emit('send', {
    message,
    files: uploadedFiles.value,
    model: props.currentModel,
    styles: selectedStyles.value
  })

  // 清空输入
  inputValue.value = ''
  clearFiles()
  selectedStyles.value = []
  clearSuggestions()

  nextTick(() => autoResize())
}

const handleStop = () => {
  emit('stop')
}

const handleModelChange = (model: any) => {
  emit('model-change', model)
}

const handleStyleSelect = (style: string) => {
  if (!selectedStyles.value.includes(style)) {
    selectedStyles.value.push(style)
  }
}

const triggerFileUpload = () => {
  fileInputRef.value?.click()
}

const handleFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (!files) return

  for (const file of files) {
    await uploadFile(file)
  }

  // 清空input值，允许重复选择同一文件
  target.value = ''

  emit('file-upload', Array.from(files))
}

// 自动调整高度
const autoResize = () => {
  if (!textareaRef.value) return

  const textarea = textareaRef.value
  textarea.style.height = 'auto'

  const scrollHeight = textarea.scrollHeight
  const lineHeight = 24
  const maxHeight = lineHeight * props.maxRows + 16

  textarea.style.height = Math.min(scrollHeight, maxHeight) + 'px'
  textarea.style.overflowY = scrollHeight > maxHeight ? 'auto' : 'hidden'
}

// 监听输入值变化
watch(() => props.modelValue, () => {
  nextTick(() => autoResize())
}, { immediate: true })

// 组件挂载
onMounted(() => {
  if (textareaRef.value && !isMobile.value) {
    textareaRef.value.focus()
  }
})
</script>

<style scoped>
/* 统一输入组件样式 */
.unified-input-container {
  @apply relative w-full;
}

/* 模式样式 */
.mode-centered {
  @apply flex items-center justify-center min-h-screen;
}

.mode-centered .input-wrapper {
  @apply w-full max-w-4xl mx-auto;
}

.mode-compact .input-wrapper {
  @apply bg-white bg-opacity-95 dark:bg-gray-800 dark:bg-opacity-95 rounded-lg border border-gray-200 dark:border-gray-700;
}

/* 输入包装器 */
.input-wrapper {
  @apply relative bg-white bg-opacity-98 dark:bg-gray-800 dark:bg-opacity-98 rounded-xl border border-gray-200 border-opacity-60 dark:border-gray-700 dark:border-opacity-60
         shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-md;
}

.input-wrapper.is-focused {
  @apply border-blue-400 dark:border-blue-500;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.input-wrapper.has-error {
  @apply border-red-400 dark:border-red-500;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

.input-wrapper.is-streaming {
  @apply border-orange-400 dark:border-orange-500;
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.2);
}

/* 文件预览区域 */
.file-preview-area {
  @apply flex flex-wrap gap-2 p-3 border-b border-gray-200 dark:border-gray-700;
}

.file-preview-item {
  @apply relative group bg-gray-50 dark:bg-gray-700 rounded-lg p-2 flex items-center gap-2;
}

.file-preview-item img {
  @apply w-12 h-12 object-cover rounded;
}

.file-icon {
  @apply flex items-center gap-2;
}

.file-name {
  @apply text-sm text-gray-600 dark:text-gray-300 truncate max-w-24;
}

.remove-file-btn {
  @apply absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full
         flex items-center justify-center opacity-0 group-hover:opacity-100
         transition-opacity duration-200 hover:bg-red-600;
}

/* 主输入区域 */
.main-input-area {
  @apply flex items-end gap-3 p-3;
}

/* 工具栏 */
.input-toolbar {
  @apply flex items-center gap-2;
}

.toolbar-btn {
  @apply p-2 rounded-full bg-gray-100 bg-opacity-80 dark:bg-gray-700 dark:bg-opacity-80
         hover:bg-blue-100 dark:hover:bg-blue-900 dark:hover:bg-opacity-50
         transition-all duration-300 hover:scale-110 active:scale-95
         backdrop-blur-sm group disabled:opacity-50 disabled:cursor-not-allowed;
}

.toolbar-btn:focus {
  @apply outline-none ring-2 ring-blue-500 ring-opacity-50;
}

/* 输入框容器 */
.textarea-container {
  @apply flex-1 relative;
}

.main-textarea {
  @apply w-full border-0 bg-transparent text-gray-800 dark:text-gray-200
         placeholder:text-gray-400 dark:placeholder:text-gray-500
         resize-none focus:outline-none text-base leading-6 py-2 px-0
         transition-all duration-300;
}

.main-textarea:focus {
  @apply ring-0 outline-none;
}

.char-count {
  @apply absolute bottom-1 right-1 text-xs text-gray-400 dark:text-gray-500;
}

/* 发送按钮区域 */
.send-button-area {
  @apply flex items-end;
}

.send-btn {
  @apply rounded-full p-2.5 transition-all duration-300
         transform hover:scale-110 active:scale-95 relative overflow-hidden;
}

.send-btn-enabled {
  @apply bg-gradient-to-r from-blue-600 via-blue-500 to-indigo-600
         hover:from-blue-700 hover:via-blue-600 hover:to-indigo-700
         text-white shadow-lg;
  box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.25), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
}

.send-btn-enabled:hover {
  box-shadow: 0 20px 25px -5px rgba(37, 99, 235, 0.4), 0 10px 10px -5px rgba(37, 99, 235, 0.04);
}

.send-btn-disabled {
  @apply bg-gray-300 dark:bg-gray-700 text-gray-500 cursor-not-allowed;
}

.stop-btn {
  @apply rounded-full p-2.5 bg-gradient-to-r from-red-500 to-red-600
         hover:from-red-600 hover:to-red-700 text-white shadow-lg
         transition-all duration-300 transform hover:scale-110 active:scale-95;
  box-shadow: 0 10px 15px -3px rgba(239, 68, 68, 0.25), 0 4px 6px -2px rgba(239, 68, 68, 0.05);
}

.stop-btn:hover {
  box-shadow: 0 20px 25px -5px rgba(220, 38, 38, 0.4), 0 10px 10px -5px rgba(220, 38, 38, 0.04);
}

/* 错误消息 */
.error-message {
  @apply px-3 pb-2 text-sm text-red-600 dark:text-red-400;
}

/* 建议列表 */
.suggestions-list {
  @apply absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800
         rounded-lg border border-gray-200 dark:border-gray-700
         shadow-lg max-h-48 overflow-y-auto z-50;
}

.suggestion-item {
  @apply flex items-center gap-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-700
         cursor-pointer transition-colors duration-200;
}

.suggestion-item.active {
  @apply bg-blue-50 dark:bg-blue-900 dark:bg-opacity-30;
}

.suggestion-avatar {
  @apply w-8 h-8 rounded-full object-cover;
}

.suggestion-content {
  @apply flex-1 min-w-0;
}

.suggestion-name {
  @apply font-medium text-gray-900 dark:text-gray-100 truncate;
}

.suggestion-desc {
  @apply text-sm text-gray-500 dark:text-gray-400 truncate;
}

/* 移动端优化 */
.is-mobile .input-wrapper {
  @apply rounded-lg shadow-md;
}

.is-mobile .main-input-area {
  @apply gap-2 p-2;
}

.is-mobile .toolbar-btn {
  @apply p-3 min-w-[44px] min-h-[44px];
}

.is-mobile .send-btn,
.is-mobile .stop-btn {
  @apply p-3 min-w-[44px] min-h-[44px];
}

/* 无障碍优化 */
@media (prefers-reduced-motion: reduce) {
  .unified-input-container * {
    @apply transition-none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .input-wrapper {
    @apply border-2 border-gray-800 dark:border-gray-200;
  }

  .send-btn-enabled {
    @apply bg-blue-700 border-2 border-blue-800;
  }
}

/* 自定义滚动条 */
.main-textarea::-webkit-scrollbar {
  @apply w-1;
}

.main-textarea::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

.main-textarea::-webkit-scrollbar-track {
  @apply bg-transparent;
}
</style>
