<template>
  <div class="modern-chat-layout chat-container" :class="layoutClasses">
    <!-- 侧边栏 -->
    <aside
      v-if="showSidebar"
      class="chat-sidebar"
      :class="{ 'sidebar-collapsed': sidebarCollapsed }"
    >
      <div class="sidebar-header">
        <h2 class="sidebar-title">对话历史</h2>
        <button
          @click="toggleSidebar"
          class="sidebar-toggle-btn chat-btn-icon"
          :aria-label="sidebarCollapsed ? '展开侧边栏' : '收起侧边栏'"
        >
          <ChevronLeftIcon v-if="!sidebarCollapsed" />
          <ChevronRightIcon v-else />
        </button>
      </div>

      <div class="sidebar-content">
        <button @click="createNewChat" class="new-chat-btn chat-btn-primary">
          <PlusIcon />
          <span v-if="!sidebarCollapsed">新建对话</span>
        </button>

        <div class="chat-history">
          <div
            v-for="chat in chatHistory"
            :key="chat.id"
            @click="selectChat(chat)"
            class="chat-history-item"
            :class="{ active: chat.id === activeChatId }"
          >
            <div class="chat-item-content">
              <div class="chat-title">{{ chat.title }}</div>
              <div v-if="!sidebarCollapsed" class="chat-preview">{{ chat.lastMessage }}</div>
              <div v-if="!sidebarCollapsed" class="chat-time">{{ formatTime(chat.updatedAt) }}</div>
            </div>
            <div v-if="!sidebarCollapsed" class="chat-actions">
              <button
                @click.stop="editChat(chat)"
                class="chat-action-btn"
                aria-label="编辑对话"
              >
                <EditIcon />
              </button>
              <button
                @click.stop="deleteChat(chat)"
                class="chat-action-btn delete"
                aria-label="删除对话"
              >
                <DeleteIcon />
              </button>
            </div>
          </div>
        </div>
      </div>
    </aside>

    <!-- 主聊天区域 -->
    <main class="chat-main" :style="mainStyle">
      <!-- 聊天头部 -->
      <header class="chat-header">
        <div class="header-left">
          <button
            v-if="!showSidebar || isMobile"
            @click="toggleSidebar"
            class="sidebar-toggle-btn chat-btn-icon"
            :aria-label="showSidebar ? '隐藏侧边栏' : '显示侧边栏'"
          >
            <MenuIcon />
          </button>

          <div class="conversation-info">
            <h1 class="conversation-title">{{ currentChatTitle }}</h1>
            <div class="conversation-meta">
              <span class="model-info">{{ currentModel }}</span>
              <span class="message-count">{{ messageCount }} 条消息</span>
            </div>
          </div>
        </div>

        <div class="header-right">
          <button
            @click="clearChat"
            class="clear-chat-btn chat-btn-secondary"
            :disabled="messageCount === 0"
            aria-label="清空对话"
          >
            <TrashIcon />
            <span v-if="!isMobile">清空</span>
          </button>

          <button
            @click="exportChat"
            class="export-chat-btn chat-btn-secondary"
            :disabled="messageCount === 0"
            aria-label="导出对话"
          >
            <DownloadIcon />
            <span v-if="!isMobile">导出</span>
          </button>
        </div>
      </header>

      <!-- 聊天消息区域 -->
      <div class="chat-messages-container">
        <div
          ref="messagesRef"
          class="chat-messages"
          @scroll="handleScroll"
        >
          <!-- 欢迎界面 -->
          <div v-if="messages.length === 0" class="welcome-screen chat-fade-in">
            <div class="welcome-content">
              <div class="welcome-icon">
                <BotIcon />
              </div>
              <h2 class="welcome-title">欢迎使用AI助手</h2>
              <p class="welcome-description">我可以帮助您解答问题、创作内容、分析数据等。请输入您的问题开始对话。</p>

              <div class="quick-actions">
                <button
                  v-for="action in quickActions"
                  :key="action.id"
                  @click="handleQuickAction(action)"
                  class="quick-action-btn"
                >
                  <span class="action-icon">{{ action.icon }}</span>
                  <span class="action-text">{{ action.text }}</span>
                </button>
              </div>
            </div>
          </div>

          <!-- 消息列表 -->
          <div v-else class="messages-list">
            <ModernChatMessage
              v-for="(message, index) in messages"
              :key="message.id"
              :message-id="message.id"
              :content="message.content"
              :sender-name="message.senderName"
              :avatar="message.avatar"
              :timestamp="message.timestamp"
              :is-user="message.isUser"
              :is-loading="message.isLoading"
              :has-error="message.hasError"
              :error-message="message.errorMessage"
              :attachments="message.attachments"
              :is-markdown="message.isMarkdown"
              :can-regenerate="!message.isUser && index === messages.length - 1"
              :can-delete="true"
              :show-feedback="!message.isUser"
              :feedback="message.feedback"
              @copy="handleCopy(message)"
              @regenerate="handleRegenerate(message)"
              @delete="handleDeleteMessage(message)"
              @feedback="handleFeedback(message, $event)"
              @attachment-click="handleAttachmentClick"
            />
          </div>
        </div>

        <!-- 滚动到底部按钮 -->
        <button
          v-if="showScrollToBottom"
          @click="scrollToBottom"
          class="scroll-to-bottom-btn"
          aria-label="滚动到底部"
        >
          <ArrowDownIcon />
        </button>
      </div>

      <!-- 输入区域 -->
      <footer class="chat-footer">
        <ModernChatInput
          v-model="inputMessage"
          :placeholder="inputPlaceholder"
          :disabled="isStreaming"
          :is-streaming="isStreaming"
          :selected-app="selectedApp"
          @send="handleSendMessage"
          @stop="handleStopGeneration"
          @file-upload="handleFileUpload"
          @app-select="handleAppSelect"
        />
      </footer>
    </main>

    <!-- 预览面板 -->
    <aside v-if="showPreview" class="chat-preview-panel">
      <div class="preview-header">
        <h3 class="preview-title">预览</h3>
        <button
          @click="closePreview"
          class="preview-close-btn chat-btn-icon"
          aria-label="关闭预览"
        >
          <CloseIcon />
        </button>
      </div>

      <div class="preview-content">
        <slot name="preview" />
      </div>
    </aside>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, nextTick, onMounted, onUnmounted } from 'vue'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { useResponsiveLayout } from '@/composables/useResponsiveLayout'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

// 组件导入
import ModernChatMessage from '../ChatMessage/ModernChatMessage.vue'
import ModernChatInput from '../ChatInput/ModernChatInput.vue'

// 图标组件
import ChevronLeftIcon from './icons/ChevronLeftIcon.vue'
import ChevronRightIcon from './icons/ChevronRightIcon.vue'
import PlusIcon from './icons/PlusIcon.vue'
import MenuIcon from './icons/MenuIcon.vue'
import EditIcon from './icons/EditIcon.vue'
import DeleteIcon from './icons/DeleteIcon.vue'
import TrashIcon from './icons/TrashIcon.vue'
import DownloadIcon from './icons/DownloadIcon.vue'
import BotIcon from './icons/BotIcon.vue'
import ArrowDownIcon from './icons/ArrowDownIcon.vue'
import CloseIcon from './icons/CloseIcon.vue'

interface ChatItem {
  id: string
  title: string
  lastMessage: string
  updatedAt: Date
}

interface Message {
  id: string
  content: string
  senderName: string
  avatar?: string
  timestamp: Date
  isUser: boolean
  isLoading?: boolean
  hasError?: boolean
  errorMessage?: string
  attachments?: any[]
  isMarkdown?: boolean
  feedback?: 'positive' | 'negative' | null
}

interface QuickAction {
  id: string
  icon: string
  text: string
  action: string
}

interface Props {
  messages?: Message[]
  chatHistory?: ChatItem[]
  activeChatId?: string
  currentChatTitle?: string
  currentModel?: string
  isStreaming?: boolean
  showSidebar?: boolean
  showPreview?: boolean
  selectedApp?: any
}

interface Emits {
  (e: 'send-message', data: { message: string; files: File[]; app: any }): void
  (e: 'stop-generation'): void
  (e: 'select-chat', chat: ChatItem): void
  (e: 'create-new-chat'): void
  (e: 'delete-chat', chat: ChatItem): void
  (e: 'clear-chat'): void
  (e: 'export-chat'): void
  (e: 'regenerate-message', message: Message): void
  (e: 'delete-message', message: Message): void
  (e: 'feedback-message', message: Message, feedback: { type: 'positive' | 'negative', text?: string }): void
}

const props = withDefaults(defineProps<Props>(), {
  messages: () => [],
  chatHistory: () => [],
  activeChatId: '',
  currentChatTitle: '新对话',
  currentModel: 'GPT-4',
  isStreaming: false,
  showSidebar: true,
  showPreview: false,
  selectedApp: null
})

const emit = defineEmits<Emits>()

// 基础设置
const { isMobile } = useBasicLayout()
const { layoutConfig } = useResponsiveLayout()

// 状态管理
const messagesRef = ref<HTMLElement>()
const inputMessage = ref('')
const sidebarCollapsed = ref(false)
const showScrollToBottom = ref(false)

// 快速操作
const quickActions: QuickAction[] = [
  { id: '1', icon: '💡', text: '创意写作', action: 'creative-writing' },
  { id: '2', icon: '📊', text: '数据分析', action: 'data-analysis' },
  { id: '3', icon: '🔍', text: '问题解答', action: 'question-answer' },
  { id: '4', icon: '📝', text: '文档总结', action: 'document-summary' }
]

// 计算属性
const layoutClasses = computed(() => ({
  'mobile-layout': isMobile.value,
  'sidebar-visible': props.showSidebar && !sidebarCollapsed.value,
  'preview-visible': props.showPreview
}))

const messageCount = computed(() => props.messages.length)

const inputPlaceholder = computed(() =>
  isMobile.value ? '输入消息...' : '输入您的问题，按 Enter 发送'
)

const mainStyle = computed(() => {
  const styles: Record<string, string> = {}

  if (props.showSidebar && !sidebarCollapsed.value && !isMobile.value) {
    styles.marginLeft = '280px'
  }

  if (props.showPreview && !isMobile.value) {
    styles.marginRight = '320px'
  }

  return styles
})

// 方法
const toggleSidebar = () => {
  if (isMobile.value) {
    emit('toggle-mobile-sidebar')
  } else {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }
}

const createNewChat = () => {
  emit('create-new-chat')
}

const selectChat = (chat: ChatItem) => {
  emit('select-chat', chat)
}

const editChat = (chat: ChatItem) => {
  // 实现编辑对话逻辑
}

const deleteChat = (chat: ChatItem) => {
  emit('delete-chat', chat)
}

const clearChat = () => {
  emit('clear-chat')
}

const exportChat = () => {
  emit('export-chat')
}

const closePreview = () => {
  emit('close-preview')
}

const handleQuickAction = (action: QuickAction) => {
  inputMessage.value = getQuickActionPrompt(action.action)
}

const getQuickActionPrompt = (action: string): string => {
  const prompts = {
    'creative-writing': '请帮我写一篇关于',
    'data-analysis': '请帮我分析这些数据：',
    'question-answer': '我想了解',
    'document-summary': '请帮我总结这个文档：'
  }
  return prompts[action] || ''
}

const handleSendMessage = (data: { message: string; files: File[]; app: any }) => {
  emit('send-message', data)
  inputMessage.value = ''
}

const handleStopGeneration = () => {
  emit('stop-generation')
}

const handleFileUpload = (files: File[]) => {
  // 处理文件上传
}

const handleAppSelect = (app: any) => {
  // 处理应用选择
}

const handleCopy = (message: Message) => {
  navigator.clipboard.writeText(message.content)
}

const handleRegenerate = (message: Message) => {
  emit('regenerate-message', message)
}

const handleDeleteMessage = (message: Message) => {
  emit('delete-message', message)
}

const handleFeedback = (message: Message, feedback: { type: 'positive' | 'negative', text?: string }) => {
  emit('feedback-message', message, feedback)
}

const handleAttachmentClick = (attachment: any) => {
  // 处理附件点击
}

const handleScroll = () => {
  if (!messagesRef.value) return

  const { scrollTop, scrollHeight, clientHeight } = messagesRef.value
  showScrollToBottom.value = scrollHeight - scrollTop - clientHeight > 100
}

const scrollToBottom = () => {
  if (messagesRef.value) {
    messagesRef.value.scrollTo({
      top: messagesRef.value.scrollHeight,
      behavior: 'smooth'
    })
  }
}

const formatTime = (date: Date): string => {
  return formatDistanceToNow(date, { addSuffix: true, locale: zhCN })
}

// 生命周期
onMounted(() => {
  nextTick(() => scrollToBottom())
})
</script>

<style scoped>
.modern-chat-layout {
  display: flex;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

.chat-sidebar {
  width: 280px;
  background: var(--chat-bg-secondary);
  border-right: 1px solid var(--chat-border-light);
  display: flex;
  flex-direction: column;
  transition: all var(--chat-transition-normal) var(--chat-ease-out);
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 20;
}

.sidebar-collapsed {
  width: 60px;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--chat-border-light);
  background: var(--chat-bg-primary);
}

.sidebar-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--chat-text-primary);
  margin: 0;
  transition: opacity var(--chat-transition-fast) var(--chat-ease-out);
}

.sidebar-collapsed .sidebar-title {
  opacity: 0;
  pointer-events: none;
}

.sidebar-toggle-btn {
  width: 2rem;
  height: 2rem;
  padding: 0;
  flex-shrink: 0;
}

.sidebar-content {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.new-chat-btn {
  width: 100%;
  padding: 0.75rem;
  justify-content: center;
  gap: 0.5rem;
  transition: all var(--chat-transition-normal) var(--chat-ease-out);
}

.sidebar-collapsed .new-chat-btn {
  padding: 0.75rem 0.5rem;
}

.sidebar-collapsed .new-chat-btn span {
  display: none;
}

.chat-history {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.chat-history-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--chat-transition-fast) var(--chat-ease-out);
  border: 1px solid transparent;
  background: var(--chat-bg-primary);
}

.chat-history-item:hover {
  background: var(--chat-bg-tertiary);
  transform: translateX(2px);
}

.chat-history-item.active {
  background: var(--chat-input-focus-border);
  color: white;
  border-color: var(--chat-input-focus-border);
}

.sidebar-collapsed .chat-history-item {
  padding: 0.75rem 0.5rem;
  justify-content: center;
}

.chat-item-content {
  flex: 1;
  min-width: 0;
}

.chat-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: inherit;
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-preview {
  font-size: 0.75rem;
  color: var(--chat-text-muted);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 0.25rem;
}

.chat-time {
  font-size: 0.7rem;
  color: var(--chat-text-muted);
}

.chat-actions {
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity var(--chat-transition-fast) var(--chat-ease-out);
}

.chat-history-item:hover .chat-actions {
  opacity: 1;
}

.chat-action-btn {
  width: 1.5rem;
  height: 1.5rem;
  border: none;
  border-radius: var(--radius-sm);
  background: transparent;
  color: var(--chat-text-muted);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--chat-transition-fast) var(--chat-ease-out);
}

.chat-action-btn:hover {
  background: var(--chat-btn-secondary-bg);
  color: var(--chat-text-primary);
}

.chat-action-btn.delete:hover {
  background: #ef4444;
  color: white;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: all var(--chat-transition-normal) var(--chat-ease-out);
  min-width: 0;
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background: var(--chat-bg-primary);
  border-bottom: 1px solid var(--chat-border-light);
  backdrop-filter: blur(8px);
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  min-width: 0;
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--chat-text-primary);
  margin: 0 0 0.25rem 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.conversation-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.75rem;
  color: var(--chat-text-muted);
}

.header-right {
  display: flex;
  gap: 0.5rem;
}

.clear-chat-btn,
.export-chat-btn {
  padding: 0.5rem 0.75rem;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.chat-messages-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.chat-messages {
  height: 100%;
  overflow-y: auto;
  padding: 1rem 1.5rem;
  scroll-behavior: smooth;
}

.welcome-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.welcome-content {
  max-width: 500px;
  padding: 2rem;
}

.welcome-icon {
  width: 4rem;
  height: 4rem;
  margin: 0 auto 1.5rem;
  background: var(--chat-btn-primary-bg);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.welcome-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--chat-text-primary);
  margin: 0 0 0.75rem 0;
}

.welcome-description {
  font-size: 1rem;
  color: var(--chat-text-secondary);
  line-height: 1.6;
  margin: 0 0 2rem 0;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--chat-bg-secondary);
  border: 1px solid var(--chat-border-light);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--chat-transition-normal) var(--chat-ease-out);
  text-align: left;
}

.quick-action-btn:hover {
  background: var(--chat-bg-tertiary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.action-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.action-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--chat-text-primary);
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.scroll-to-bottom-btn {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  width: 2.5rem;
  height: 2.5rem;
  border: none;
  border-radius: var(--radius-full);
  background: var(--chat-btn-primary-bg);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-lg);
  transition: all var(--chat-transition-normal) var(--chat-ease-out);
  z-index: 10;
}

.scroll-to-bottom-btn:hover {
  background: var(--chat-btn-primary-hover);
  transform: scale(1.05);
}

.chat-footer {
  padding: 1rem 1.5rem;
  background: var(--chat-bg-primary);
  border-top: 1px solid var(--chat-border-light);
}

.chat-preview-panel {
  width: 320px;
  background: var(--chat-bg-secondary);
  border-left: 1px solid var(--chat-border-light);
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 20;
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--chat-border-light);
  background: var(--chat-bg-primary);
}

.preview-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--chat-text-primary);
  margin: 0;
}

.preview-close-btn {
  width: 2rem;
  height: 2rem;
  padding: 0;
}

.preview-content {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-sidebar {
    transform: translateX(-100%);
    transition: transform var(--chat-transition-normal) var(--chat-ease-out);
  }

  .sidebar-visible .chat-sidebar {
    transform: translateX(0);
  }

  .chat-main {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  .chat-header {
    padding: 0.75rem 1rem;
  }

  .chat-messages {
    padding: 1rem;
  }

  .chat-footer {
    padding: 0.75rem 1rem;
  }

  .conversation-title {
    font-size: 1rem;
  }

  .conversation-meta {
    gap: 0.5rem;
  }

  .header-right {
    gap: 0.25rem;
  }

  .clear-chat-btn span,
  .export-chat-btn span {
    display: none;
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }

  .chat-preview-panel {
    display: none;
  }
}

@media (max-width: 480px) {
  .welcome-content {
    padding: 1rem;
  }

  .welcome-title {
    font-size: 1.25rem;
  }

  .welcome-description {
    font-size: 0.875rem;
  }

  .quick-action-btn {
    padding: 0.75rem;
  }
}

/* 无障碍性增强 */
@media (prefers-reduced-motion: reduce) {
  .chat-sidebar,
  .chat-main,
  .chat-history-item,
  .quick-action-btn,
  .scroll-to-bottom-btn {
    transition: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .chat-sidebar,
  .chat-main,
  .chat-preview-panel {
    border-width: 2px;
  }

  .chat-history-item,
  .quick-action-btn {
    border-width: 2px;
  }
}
</style>
