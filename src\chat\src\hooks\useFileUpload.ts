import { ref } from 'vue'

export interface UploadedFile {
  name: string
  type: string
  size: number
  preview?: string
  url?: string
  file?: File
}

export function useFileUpload() {
  const uploadedFiles = ref<UploadedFile[]>([])
  const isUploading = ref(false)
  const uploadProgress = ref(0)

  const uploadFile = async (file: File): Promise<UploadedFile> => {
    isUploading.value = true
    uploadProgress.value = 0

    try {
      // 创建预览URL（如果是图片）
      let preview: string | undefined
      if (file.type.startsWith('image/')) {
        preview = URL.createObjectURL(file)
      }

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        uploadProgress.value += 10
        if (uploadProgress.value >= 90) {
          clearInterval(progressInterval)
        }
      }, 100)

      // 这里应该是实际的上传逻辑
      // const uploadResult = await uploadToServer(file)
      
      // 模拟上传延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      clearInterval(progressInterval)
      uploadProgress.value = 100

      const uploadedFile: UploadedFile = {
        name: file.name,
        type: file.type,
        size: file.size,
        preview,
        file,
        // url: uploadResult.url // 实际上传后的URL
      }

      uploadedFiles.value.push(uploadedFile)
      
      return uploadedFile
    } catch (error) {
      console.error('文件上传失败:', error)
      throw error
    } finally {
      isUploading.value = false
      uploadProgress.value = 0
    }
  }

  const removeFile = (index: number) => {
    const file = uploadedFiles.value[index]
    if (file?.preview) {
      URL.revokeObjectURL(file.preview)
    }
    uploadedFiles.value.splice(index, 1)
  }

  const clearFiles = () => {
    uploadedFiles.value.forEach(file => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview)
      }
    })
    uploadedFiles.value = []
  }

  const validateFile = (file: File, options?: {
    maxSize?: number
    allowedTypes?: string[]
  }): boolean => {
    const { maxSize = 10 * 1024 * 1024, allowedTypes } = options || {} // 默认10MB

    if (file.size > maxSize) {
      throw new Error(`文件大小不能超过 ${Math.round(maxSize / 1024 / 1024)}MB`)
    }

    if (allowedTypes && !allowedTypes.some(type => file.type.includes(type))) {
      throw new Error(`不支持的文件类型: ${file.type}`)
    }

    return true
  }

  return {
    uploadedFiles,
    isUploading,
    uploadProgress,
    uploadFile,
    removeFile,
    clearFiles,
    validateFile
  }
}
