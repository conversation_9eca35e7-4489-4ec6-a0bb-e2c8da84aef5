<template>
  <svg
    class="file-icon"
    :class="iconClass"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      v-if="iconType === 'image'"
      d="M4 4C4 2.89543 4.89543 2 6 2H14L20 8V20C20 21.1046 19.1046 22 18 22H6C4.89543 22 4 21.1046 4 20V4Z"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      v-else-if="iconType === 'pdf'"
      d="M4 4C4 2.89543 4.89543 2 6 2H14L20 8V20C20 21.1046 19.1046 22 18 22H6C4.89543 22 4 21.1046 4 20V4Z"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
      fill="rgba(239, 68, 68, 0.1)"
    />
    <path
      v-else-if="iconType === 'document'"
      d="M4 4C4 2.89543 4.89543 2 6 2H14L20 8V20C20 21.1046 19.1046 22 18 22H6C4.89543 22 4 21.1046 4 20V4Z"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
      fill="rgba(59, 130, 246, 0.1)"
    />
    <path
      v-else
      d="M4 4C4 2.89543 4.89543 2 6 2H14L20 8V20C20 21.1046 19.1046 22 18 22H6C4.89543 22 4 21.1046 4 20V4Z"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    
    <!-- 文件类型标识 -->
    <text
      v-if="showExtension"
      x="12"
      y="16"
      text-anchor="middle"
      class="text-xs font-medium"
      fill="currentColor"
    >
      {{ extension }}
    </text>
  </svg>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  type: string
  size?: number
  showExtension?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 20,
  showExtension: true
})

const iconType = computed(() => {
  const type = props.type.toLowerCase()
  
  if (type.startsWith('image/')) return 'image'
  if (type === 'application/pdf') return 'pdf'
  if (type.includes('document') || type.includes('text')) return 'document'
  
  return 'file'
})

const extension = computed(() => {
  const type = props.type.toLowerCase()
  
  if (type.startsWith('image/')) return 'IMG'
  if (type === 'application/pdf') return 'PDF'
  if (type.includes('document')) return 'DOC'
  if (type.includes('text')) return 'TXT'
  if (type.includes('json')) return 'JSON'
  if (type.includes('javascript')) return 'JS'
  
  return 'FILE'
})

const iconClass = computed(() => ({
  'text-green-500': iconType.value === 'image',
  'text-red-500': iconType.value === 'pdf',
  'text-blue-500': iconType.value === 'document',
  'text-gray-500': iconType.value === 'file'
}))
</script>

<style scoped>
.file-icon {
  width: v-bind(props.size + 'px');
  height: v-bind(props.size + 'px');
}
</style>
