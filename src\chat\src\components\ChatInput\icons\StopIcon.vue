<template>
  <svg
    class="stop-icon"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="6"
      y="6"
      width="12"
      height="12"
      rx="2"
      fill="currentColor"
    />
  </svg>
</template>

<script setup lang="ts">
interface Props {
  size?: number
}

const props = withDefaults(defineProps<Props>(), {
  size: 18
})
</script>

<style scoped>
.stop-icon {
  width: v-bind(props.size + 'px');
  height: v-bind(props.size + 'px');
}
</style>
