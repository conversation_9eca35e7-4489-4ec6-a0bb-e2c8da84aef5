<template>
  <div 
    class="enhanced-message" 
    :class="messageClass"
    :data-message-id="messageId"
    role="article"
    :aria-label="messageAriaLabel"
  >
    <!-- 消息头部 -->
    <div class="message-header">
      <!-- 头像 -->
      <div class="avatar-container">
        <img 
          v-if="avatar" 
          :src="avatar" 
          :alt="senderName"
          class="avatar"
          @error="handleAvatarError"
        />
        <div v-else class="avatar-fallback">
          <UserIcon v-if="isUser" />
          <BotIcon v-else />
        </div>
      </div>

      <!-- 消息信息 -->
      <div class="message-info">
        <div class="sender-name">{{ senderName }}</div>
        <div class="message-time" :title="fullTimestamp">{{ relativeTime }}</div>
      </div>

      <!-- 操作按钮 -->
      <div class="message-actions">
        <button 
          v-if="!isUser && !isLoading"
          @click="handleCopy"
          class="action-btn"
          :aria-label="`复制${senderName}的消息`"
          title="复制消息"
        >
          <CopyIcon />
        </button>
        
        <button 
          v-if="!isUser && canRegenerate"
          @click="handleRegenerate"
          class="action-btn"
          :aria-label="`重新生成${senderName}的回复`"
          title="重新生成"
        >
          <RefreshIcon />
        </button>
        
        <button 
          v-if="canDelete"
          @click="handleDelete"
          class="action-btn delete-btn"
          :aria-label="`删除${senderName}的消息`"
          title="删除消息"
        >
          <DeleteIcon />
        </button>
      </div>
    </div>

    <!-- 消息内容 -->
    <div class="message-content">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-content">
        <div class="typing-indicator">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <span class="loading-text">{{ loadingText }}</span>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="hasError" class="error-content">
        <div class="error-icon">
          <ErrorIcon />
        </div>
        <div class="error-message">
          <div class="error-title">消息发送失败</div>
          <div class="error-desc">{{ errorMessage }}</div>
          <button @click="handleRetry" class="retry-btn">重试</button>
        </div>
      </div>

      <!-- 正常内容 -->
      <div v-else class="normal-content">
        <!-- 文件附件 -->
        <div v-if="attachments.length > 0" class="attachments">
          <div 
            v-for="(file, index) in attachments" 
            :key="index"
            class="attachment-item"
          >
            <img 
              v-if="file.type.startsWith('image/')" 
              :src="file.url" 
              :alt="file.name"
              class="attachment-image"
              @click="handleImagePreview(file)"
            />
            <div v-else class="attachment-file">
              <FileIcon :type="file.type" />
              <span class="file-name">{{ file.name }}</span>
              <span class="file-size">{{ formatFileSize(file.size) }}</span>
            </div>
          </div>
        </div>

        <!-- 文本内容 -->
        <div 
          v-if="content"
          class="text-content"
          :class="{ 'markdown-content': isMarkdown }"
          v-html="formattedContent"
        ></div>

        <!-- 特殊内容类型 -->
        <component 
          v-if="specialComponent"
          :is="specialComponent"
          v-bind="specialProps"
          @action="handleSpecialAction"
        />
      </div>
    </div>

    <!-- 消息反馈 -->
    <div v-if="showFeedback && !isUser && !isLoading" class="message-feedback">
      <button 
        @click="handleFeedback('like')"
        class="feedback-btn"
        :class="{ active: feedback === 'like' }"
        :aria-label="feedback === 'like' ? '取消点赞' : '点赞这条回复'"
      >
        <ThumbUpIcon />
      </button>
      <button 
        @click="handleFeedback('dislike')"
        class="feedback-btn"
        :class="{ active: feedback === 'dislike' }"
        :aria-label="feedback === 'dislike' ? '取消点踩' : '点踩这条回复'"
      >
        <ThumbDownIcon />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

// 图标组件
import UserIcon from './icons/UserIcon.vue'
import BotIcon from './icons/BotIcon.vue'
import CopyIcon from './icons/CopyIcon.vue'
import RefreshIcon from './icons/RefreshIcon.vue'
import DeleteIcon from './icons/DeleteIcon.vue'
import ErrorIcon from './icons/ErrorIcon.vue'
import FileIcon from './icons/FileIcon.vue'
import ThumbUpIcon from './icons/ThumbUpIcon.vue'
import ThumbDownIcon from './icons/ThumbDownIcon.vue'

interface Attachment {
  name: string
  type: string
  url: string
  size: number
}

interface Props {
  messageId: string
  content: string
  isUser: boolean
  senderName: string
  avatar?: string
  timestamp: Date
  isLoading?: boolean
  hasError?: boolean
  errorMessage?: string
  loadingText?: string
  attachments?: Attachment[]
  isMarkdown?: boolean
  canRegenerate?: boolean
  canDelete?: boolean
  showFeedback?: boolean
  feedback?: 'like' | 'dislike' | null
  specialType?: string
  specialProps?: Record<string, any>
}

interface Emits {
  (e: 'copy', content: string): void
  (e: 'regenerate'): void
  (e: 'delete'): void
  (e: 'retry'): void
  (e: 'feedback', type: 'like' | 'dislike'): void
  (e: 'image-preview', file: Attachment): void
  (e: 'special-action', action: string, data: any): void
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  hasError: false,
  errorMessage: '',
  loadingText: '正在思考中...',
  attachments: () => [],
  isMarkdown: true,
  canRegenerate: true,
  canDelete: true,
  showFeedback: true,
  feedback: null,
  specialType: '',
  specialProps: () => ({})
})

const emit = defineEmits<Emits>()

// 状态管理
const avatarError = ref(false)

// 计算属性
const messageClass = computed(() => ({
  'is-user': props.isUser,
  'is-bot': !props.isUser,
  'is-loading': props.isLoading,
  'has-error': props.hasError,
  'has-attachments': props.attachments.length > 0
}))

const messageAriaLabel = computed(() => {
  if (props.isLoading) return `${props.senderName}正在输入`
  if (props.hasError) return `${props.senderName}的消息发送失败`
  return `${props.senderName}在${relativeTime.value}发送的消息`
})

const relativeTime = computed(() => {
  return formatDistanceToNow(props.timestamp, { 
    addSuffix: true, 
    locale: zhCN 
  })
})

const fullTimestamp = computed(() => {
  return props.timestamp.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
})

const formattedContent = computed(() => {
  if (!props.content) return ''
  
  if (props.isMarkdown) {
    // 这里应该使用markdown解析器
    return props.content.replace(/\n/g, '<br>')
  }
  
  return props.content.replace(/\n/g, '<br>')
})

const specialComponent = computed(() => {
  if (!props.specialType) return null
  
  // 根据特殊类型返回对应组件
  const componentMap: Record<string, string> = {
    'code': 'CodeBlock',
    'image': 'ImageGeneration',
    'video': 'VideoGeneration',
    'audio': 'AudioGeneration',
    'mindmap': 'MindMap',
    'chart': 'Chart'
  }
  
  return componentMap[props.specialType] || null
})

// 事件处理
const handleAvatarError = () => {
  avatarError.value = true
}

const handleCopy = () => {
  emit('copy', props.content)
}

const handleRegenerate = () => {
  emit('regenerate')
}

const handleDelete = () => {
  emit('delete')
}

const handleRetry = () => {
  emit('retry')
}

const handleFeedback = (type: 'like' | 'dislike') => {
  emit('feedback', type)
}

const handleImagePreview = (file: Attachment) => {
  emit('image-preview', file)
}

const handleSpecialAction = (action: string, data: any) => {
  emit('special-action', action, data)
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 组件挂载
onMounted(() => {
  // 可以在这里添加一些初始化逻辑
})
</script>

<style scoped>
/* 增强消息组件样式 */
.enhanced-message {
  @apply relative mb-6 group;
}

.enhanced-message.is-user {
  @apply ml-auto max-w-[80%];
}

.enhanced-message.is-bot {
  @apply mr-auto max-w-[90%];
}

/* 消息头部 */
.message-header {
  @apply flex items-start gap-3 mb-2;
}

.enhanced-message.is-user .message-header {
  @apply flex-row-reverse;
}

/* 头像 */
.avatar-container {
  @apply flex-shrink-0;
}

.avatar {
  @apply w-8 h-8 rounded-full object-cover border-2 border-white dark:border-gray-700 shadow-sm;
}

.avatar-fallback {
  @apply w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 
         flex items-center justify-center text-white text-sm font-medium shadow-sm;
}

/* 消息信息 */
.message-info {
  @apply flex-1 min-w-0;
}

.enhanced-message.is-user .message-info {
  @apply text-right;
}

.sender-name {
  @apply font-medium text-gray-900 dark:text-gray-100 text-sm;
}

.message-time {
  @apply text-xs text-gray-500 dark:text-gray-400 mt-1;
}

/* 操作按钮 */
.message-actions {
  @apply flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200;
}

.action-btn {
  @apply p-1.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 
         transition-colors duration-200 text-gray-500 dark:text-gray-400 
         hover:text-gray-700 dark:hover:text-gray-300;
}

.action-btn:focus {
  @apply outline-none ring-2 ring-blue-500/50;
}

.delete-btn {
  @apply hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400;
}

/* 消息内容 */
.message-content {
  @apply relative;
}

.enhanced-message.is-user .message-content {
  @apply bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-2xl rounded-tr-sm p-3 shadow-sm;
}

.enhanced-message.is-bot .message-content {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 
         rounded-2xl rounded-tl-sm p-3 shadow-sm;
}

/* 加载状态 */
.loading-content {
  @apply flex items-center gap-3;
}

.typing-indicator {
  @apply flex gap-1;
}

.typing-indicator span {
  @apply w-2 h-2 bg-gray-400 rounded-full animate-pulse;
}

.typing-indicator span:nth-child(2) {
  @apply animation-delay-200;
}

.typing-indicator span:nth-child(3) {
  @apply animation-delay-400;
}

.loading-text {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

/* 错误状态 */
.error-content {
  @apply flex items-start gap-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg;
}

.error-icon {
  @apply text-red-500 dark:text-red-400 flex-shrink-0;
}

.error-message {
  @apply flex-1;
}

.error-title {
  @apply font-medium text-red-800 dark:text-red-200 text-sm;
}

.error-desc {
  @apply text-red-600 dark:text-red-300 text-sm mt-1;
}

.retry-btn {
  @apply mt-2 px-3 py-1 bg-red-500 hover:bg-red-600 text-white text-sm rounded-md 
         transition-colors duration-200;
}

/* 附件 */
.attachments {
  @apply flex flex-wrap gap-2 mb-3;
}

.attachment-item {
  @apply relative group;
}

.attachment-image {
  @apply max-w-xs rounded-lg cursor-pointer hover:opacity-90 transition-opacity duration-200;
}

.attachment-file {
  @apply flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg;
}

.file-name {
  @apply text-sm font-medium text-gray-900 dark:text-gray-100;
}

.file-size {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

/* 文本内容 */
.text-content {
  @apply text-sm leading-relaxed;
}

.enhanced-message.is-user .text-content {
  @apply text-white;
}

.enhanced-message.is-bot .text-content {
  @apply text-gray-800 dark:text-gray-200;
}

.markdown-content {
  @apply prose prose-sm dark:prose-invert max-w-none;
}

/* 消息反馈 */
.message-feedback {
  @apply flex items-center gap-2 mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200;
}

.feedback-btn {
  @apply p-1.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 
         transition-colors duration-200 text-gray-400 dark:text-gray-500;
}

.feedback-btn.active {
  @apply text-blue-500 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20;
}

.feedback-btn:focus {
  @apply outline-none ring-2 ring-blue-500/50;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .enhanced-message.is-user {
    @apply max-w-[90%];
  }
  
  .enhanced-message.is-bot {
    @apply max-w-[95%];
  }
  
  .message-actions {
    @apply opacity-100;
  }
  
  .message-feedback {
    @apply opacity-100;
  }
}

/* 无障碍优化 */
@media (prefers-reduced-motion: reduce) {
  .enhanced-message * {
    @apply transition-none;
  }
  
  .typing-indicator span {
    @apply animate-none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .enhanced-message.is-user .message-content {
    @apply border-2 border-blue-800;
  }
  
  .enhanced-message.is-bot .message-content {
    @apply border-2 border-gray-800 dark:border-gray-200;
  }
}
</style>
