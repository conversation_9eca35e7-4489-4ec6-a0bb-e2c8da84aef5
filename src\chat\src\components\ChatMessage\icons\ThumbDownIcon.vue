<template>
  <svg
    class="thumb-down-icon"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 15V19C10 19.7956 10.3161 20.5587 10.8787 21.1213C11.4413 21.6839 12.2044 22 13 22L17 15V3H5.72C5.23773 2.99448 4.76958 3.16361 4.40211 3.47596C4.03464 3.78831 3.79234 4.22305 3.72 4.7L2.34 12.7C2.29649 12.9866 2.31583 13.2793 2.39668 13.5577C2.47753 13.8362 2.61793 14.0937 2.80814 14.3125C2.99834 14.5313 3.23391 14.7061 3.49843 14.8248C3.76295 14.9435 4.05011 15.0033 4.34 15H10Z"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M17 15H20C20.5304 15 21.0391 14.7893 21.4142 14.4142C21.7893 14.0391 22 13.5304 22 13V5C22 4.46957 21.7893 3.96086 21.4142 3.58579C21.0391 3.21071 20.5304 3 20 3H17V15Z"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
</template>

<script setup lang="ts">
interface Props {
  size?: number
}

const props = withDefaults(defineProps<Props>(), {
  size: 16
})
</script>

<style scoped>
.thumb-down-icon {
  width: v-bind(props.size + 'px');
  height: v-bind(props.size + 'px');
}
</style>
