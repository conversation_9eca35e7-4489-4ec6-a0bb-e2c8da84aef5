<template>
  <div class="style-selector">
    <div class="style-header">
      <h4 class="style-title">绘画风格</h4>
      <button 
        v-if="selectedStyles.length > 0"
        @click="clearStyles"
        class="clear-btn"
      >
        清除
      </button>
    </div>

    <div class="style-grid">
      <button
        v-for="style in styles"
        :key="style.id"
        @click="toggleStyle(style)"
        class="style-option"
        :class="{ 
          active: selectedStyles.includes(style.id),
          disabled: !style.available 
        }"
        :disabled="!style.available"
        :title="style.description"
      >
        <div class="style-preview">
          <img 
            v-if="style.preview" 
            :src="style.preview" 
            :alt="style.name"
            class="style-image"
          />
          <div v-else class="style-placeholder">
            <PaletteIcon />
          </div>
        </div>
        
        <div class="style-info">
          <span class="style-name">{{ style.name }}</span>
          <span v-if="style.isPremium" class="style-premium">Pro</span>
        </div>
        
        <div v-if="selectedStyles.includes(style.id)" class="style-selected">
          <CheckIcon />
        </div>
      </button>
    </div>

    <!-- 自定义风格输入 -->
    <div class="custom-style">
      <label for="custom-style-input" class="custom-label">
        自定义风格描述
      </label>
      <input
        id="custom-style-input"
        v-model="customStyle"
        type="text"
        placeholder="例如：水彩画风格，柔和色调"
        class="custom-input"
        @keydown.enter="addCustomStyle"
      />
      <button 
        v-if="customStyle.trim()"
        @click="addCustomStyle"
        class="add-custom-btn"
      >
        添加
      </button>
    </div>

    <!-- 已选择的风格 -->
    <div v-if="selectedStylesInfo.length > 0" class="selected-styles">
      <h5 class="selected-title">已选择的风格</h5>
      <div class="selected-list">
        <span
          v-for="style in selectedStylesInfo"
          :key="style.id"
          class="selected-tag"
        >
          {{ style.name }}
          <button 
            @click="removeStyle(style.id)"
            class="remove-style-btn"
            :aria-label="`移除${style.name}风格`"
          >
            <CloseIcon :size="12" />
          </button>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 图标组件
import PaletteIcon from './icons/PaletteIcon.vue'
import CheckIcon from './icons/CheckIcon.vue'
import CloseIcon from './icons/CloseIcon.vue'

interface Style {
  id: string
  name: string
  description?: string
  preview?: string
  available: boolean
  isPremium?: boolean
  category?: string
}

interface Props {
  styles: Style[]
  maxSelection?: number
}

interface Emits {
  (e: 'select', styleId: string): void
  (e: 'deselect', styleId: string): void
  (e: 'change', selectedStyles: string[]): void
}

const props = withDefaults(defineProps<Props>(), {
  maxSelection: 5
})

const emit = defineEmits<Emits>()

// 状态管理
const selectedStyles = ref<string[]>([])
const customStyle = ref('')

// 计算属性
const selectedStylesInfo = computed(() => {
  return selectedStyles.value.map(id => {
    const style = props.styles.find(s => s.id === id)
    return style || { id, name: id, available: true }
  })
})

// 事件处理
const toggleStyle = (style: Style) => {
  if (!style.available) return
  
  const index = selectedStyles.value.indexOf(style.id)
  
  if (index > -1) {
    // 取消选择
    selectedStyles.value.splice(index, 1)
    emit('deselect', style.id)
  } else {
    // 选择风格
    if (selectedStyles.value.length >= props.maxSelection) {
      // 如果达到最大选择数量，移除第一个
      const removed = selectedStyles.value.shift()
      if (removed) {
        emit('deselect', removed)
      }
    }
    
    selectedStyles.value.push(style.id)
    emit('select', style.id)
  }
  
  emit('change', [...selectedStyles.value])
}

const removeStyle = (styleId: string) => {
  const index = selectedStyles.value.indexOf(styleId)
  if (index > -1) {
    selectedStyles.value.splice(index, 1)
    emit('deselect', styleId)
    emit('change', [...selectedStyles.value])
  }
}

const clearStyles = () => {
  const oldStyles = [...selectedStyles.value]
  selectedStyles.value = []
  
  oldStyles.forEach(styleId => {
    emit('deselect', styleId)
  })
  
  emit('change', [])
}

const addCustomStyle = () => {
  const style = customStyle.value.trim()
  if (!style) return
  
  const customId = `custom-${Date.now()}`
  
  if (selectedStyles.value.length >= props.maxSelection) {
    const removed = selectedStyles.value.shift()
    if (removed) {
      emit('deselect', removed)
    }
  }
  
  selectedStyles.value.push(customId)
  emit('select', customId)
  emit('change', [...selectedStyles.value])
  
  customStyle.value = ''
}
</script>

<style scoped>
.style-selector {
  @apply space-y-4;
}

/* 头部 */
.style-header {
  @apply flex items-center justify-between;
}

.style-title {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}

.clear-btn {
  @apply text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300
         transition-colors duration-200;
}

/* 风格网格 */
.style-grid {
  @apply grid grid-cols-2 sm:grid-cols-3 gap-3;
}

.style-option {
  @apply relative p-3 border border-gray-200 dark:border-gray-700 rounded-lg
         hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200
         disabled:opacity-50 disabled:cursor-not-allowed;
}

.style-option.active {
  @apply border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20;
}

.style-preview {
  @apply w-full aspect-square rounded-md overflow-hidden mb-2 bg-gray-100 dark:bg-gray-700;
}

.style-image {
  @apply w-full h-full object-cover;
}

.style-placeholder {
  @apply w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500;
}

.style-info {
  @apply flex items-center justify-between;
}

.style-name {
  @apply text-xs font-medium text-gray-900 dark:text-gray-100 truncate;
}

.style-premium {
  @apply text-xs bg-gradient-to-r from-yellow-400 to-orange-500 
         text-white px-1.5 py-0.5 rounded-full font-medium;
}

.style-selected {
  @apply absolute top-2 right-2 w-5 h-5 bg-blue-500 text-white rounded-full
         flex items-center justify-center;
}

/* 自定义风格 */
.custom-style {
  @apply space-y-2;
}

.custom-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.custom-input {
  @apply w-full px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-lg
         bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
         placeholder:text-gray-400 dark:placeholder:text-gray-500
         focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500
         transition-all duration-200;
}

.add-custom-btn {
  @apply px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-md
         transition-colors duration-200;
}

/* 已选择的风格 */
.selected-styles {
  @apply space-y-2;
}

.selected-title {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}

.selected-list {
  @apply flex flex-wrap gap-2;
}

.selected-tag {
  @apply inline-flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30
         text-blue-700 dark:text-blue-300 text-xs rounded-full;
}

.remove-style-btn {
  @apply hover:bg-blue-200 dark:hover:bg-blue-800 rounded-full p-0.5
         transition-colors duration-200;
}

/* 响应式优化 */
@media (max-width: 640px) {
  .style-grid {
    @apply grid-cols-2 gap-2;
  }
  
  .style-option {
    @apply p-2;
  }
  
  .style-name {
    @apply text-xs;
  }
}
</style>
