<template>
  <div class="test-improved-chat">
    <div class="test-container">
      <h1 class="test-title">聊天页面UI改进测试</h1>

      <!-- 测试模型选择器 -->
      <div class="test-section">
        <h2 class="section-title">模型选择器测试</h2>
        <div class="test-model-selector">
          <ModelSelector
            :current-model="currentModel"
            :models="availableModels"
            @change="handleModelChange"
          />
          <div class="current-model-info">
            <h3>当前选择的模型:</h3>
            <pre>{{ JSON.stringify(currentModel, null, 2) }}</pre>
          </div>
        </div>
      </div>

      <!-- 测试统一输入组件 -->
      <div class="test-section">
        <h2 class="section-title">统一输入组件测试</h2>
        <div class="test-input-container">
          <UnifiedInput
            v-model="testMessage"
            placeholder="测试输入组件..."
            :current-model="currentModel"
            :is-streaming="isStreaming"
            @send="handleSendMessage"
            @stop="handleStopGeneration"
            @model-change="handleModelChange"
            @file-upload="handleFileUpload"
          />
        </div>
      </div>

      <!-- 测试消息组件 -->
      <div class="test-section">
        <h2 class="section-title">消息组件测试</h2>
        <div class="test-messages">
          <EnhancedMessage
            v-for="message in testMessages"
            :key="message.id"
            :message-id="message.id"
            :content="message.content"
            :is-user="message.isUser"
            :sender-name="message.senderName"
            :avatar="message.avatar"
            :timestamp="message.timestamp"
            :is-loading="message.isLoading"
            :has-error="message.hasError"
            :error-message="message.errorMessage"
            :attachments="message.attachments || []"
            @copy="handleMessageCopy"
            @regenerate="handleMessageRegenerate"
            @delete="handleMessageDelete"
            @retry="handleMessageRetry"
            @feedback="handleMessageFeedback"
          />
        </div>
      </div>

      <!-- 测试响应式布局 -->
      <div class="test-section">
        <h2 class="section-title">响应式布局测试</h2>
        <div class="layout-info">
          <p>当前屏幕宽度: {{ screenWidth }}px</p>
          <p>设备类型: {{ deviceType }}</p>
          <p>是否移动端: {{ isMobile ? '是' : '否' }}</p>
        </div>

        <div class="layout-controls">
          <button
            @click="simulateResize(375)"
            class="test-btn"
          >
            模拟手机 (375px)
          </button>
          <button
            @click="simulateResize(768)"
            class="test-btn"
          >
            模拟平板 (768px)
          </button>
          <button
            @click="simulateResize(1024)"
            class="test-btn"
          >
            模拟桌面 (1024px)
          </button>
          <button
            @click="resetSize"
            class="test-btn"
          >
            重置尺寸
          </button>
        </div>
      </div>

      <!-- 测试状态 -->
      <div class="test-section">
        <h2 class="section-title">状态控制</h2>
        <div class="state-controls">
          <button
            @click="toggleStreaming"
            class="test-btn"
            :class="{ active: isStreaming }"
          >
            {{ isStreaming ? '停止流式输出' : '开始流式输出' }}
          </button>

          <button
            @click="addTestMessage"
            class="test-btn"
          >
            添加测试消息
          </button>

          <button
            @click="addErrorMessage"
            class="test-btn"
          >
            添加错误消息
          </button>

          <button
            @click="clearMessages"
            class="test-btn"
          >
            清空消息
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { getAvailableModels, getDefaultModel } from '@/data/mockModels'
import { getAvailableStyles } from '@/data/mockStyles'

// 组件导入
import UnifiedInput from '@/components/ChatInput/UnifiedInput.vue'
import EnhancedMessage from '@/components/ChatMessage/EnhancedMessage.vue'
import ModelSelector from '@/components/ChatInput/ModelSelector.vue'

// 测试数据
const testMessage = ref('')
const isStreaming = ref(false)
const testMessages = ref([
  {
    id: '1',
    content: '你好！这是一条测试用户消息。',
    isUser: true,
    senderName: '测试用户',
    timestamp: new Date(Date.now() - 60000),
    attachments: []
  },
  {
    id: '2',
    content: '你好！我是AI助手，很高兴为您服务。这是一条测试AI回复消息，包含了一些**Markdown格式**的文本。\n\n- 列表项1\n- 列表项2\n- 列表项3',
    isUser: false,
    senderName: 'AI助手',
    timestamp: new Date(Date.now() - 30000),
    attachments: []
  }
])

// 使用真实的模型数据
const availableModels = ref(getAvailableModels())
const availableStyles = ref(getAvailableStyles())
const currentModel = ref(getDefaultModel())

// 响应式布局测试
const { isMobile } = useBasicLayout()
const screenWidth = ref(window.innerWidth)
const deviceType = computed(() => {
  if (screenWidth.value < 768) return 'mobile'
  if (screenWidth.value < 1024) return 'tablet'
  return 'desktop'
})

// 事件处理
const handleSendMessage = (data: any) => {
  console.log('发送消息:', data)

  // 添加用户消息
  testMessages.value.push({
    id: Date.now().toString(),
    content: data.message,
    isUser: true,
    senderName: '测试用户',
    timestamp: new Date(),
    attachments: data.files || []
  })

  // 清空输入
  testMessage.value = ''

  // 模拟AI回复
  setTimeout(() => {
    testMessages.value.push({
      id: (Date.now() + 1).toString(),
      content: `这是对"${data.message}"的AI回复。`,
      isUser: false,
      senderName: currentModel.value.name,
      timestamp: new Date(),
      attachments: []
    })
  }, 1000)
}

const handleStopGeneration = () => {
  console.log('停止生成')
  isStreaming.value = false
}

const handleModelChange = (model: any) => {
  console.log('模型切换:', model)
  currentModel.value = model
}

const handleFileUpload = (files: File[]) => {
  console.log('文件上传:', files)
}

const handleMessageCopy = (content: string) => {
  console.log('复制消息:', content)
  navigator.clipboard?.writeText(content)
}

const handleMessageRegenerate = () => {
  console.log('重新生成消息')
}

const handleMessageDelete = () => {
  console.log('删除消息')
}

const handleMessageRetry = () => {
  console.log('重试消息')
}

const handleMessageFeedback = (type: 'like' | 'dislike') => {
  console.log('消息反馈:', type)
}

// 测试控制
const toggleStreaming = () => {
  isStreaming.value = !isStreaming.value
}

const addTestMessage = () => {
  testMessages.value.push({
    id: Date.now().toString(),
    content: '这是一条新的测试消息，时间：' + new Date().toLocaleTimeString(),
    isUser: Math.random() > 0.5,
    senderName: Math.random() > 0.5 ? '测试用户' : 'AI助手',
    timestamp: new Date(),
    attachments: []
  })
}

const addErrorMessage = () => {
  testMessages.value.push({
    id: Date.now().toString(),
    content: '这是一条错误消息',
    isUser: false,
    senderName: 'AI助手',
    timestamp: new Date(),
    hasError: true,
    errorMessage: '网络连接失败，请重试',
    attachments: []
  })
}

const clearMessages = () => {
  testMessages.value = []
}

const simulateResize = (width: number) => {
  screenWidth.value = width
  // 这里可以触发实际的窗口大小变化事件
  window.dispatchEvent(new Event('resize'))
}

const resetSize = () => {
  screenWidth.value = window.innerWidth
}

// 监听窗口大小变化
const updateScreenSize = () => {
  screenWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', updateScreenSize)
})
</script>

<style scoped>
.test-improved-chat {
  @apply min-h-screen bg-gray-50 dark:bg-gray-900 p-4;
}

.test-container {
  @apply max-w-4xl mx-auto space-y-8;
}

.test-title {
  @apply text-3xl font-bold text-gray-900 dark:text-gray-100 text-center mb-8;
}

.test-section {
  @apply bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700;
}

.section-title {
  @apply text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4;
}

.test-model-selector {
  @apply space-y-4;
}

.current-model-info {
  @apply bg-gray-50 dark:bg-gray-700 rounded-lg p-4;
}

.current-model-info h3 {
  @apply text-sm font-medium text-gray-900 dark:text-gray-100 mb-2;
}

.current-model-info pre {
  @apply text-xs text-gray-600 dark:text-gray-300 bg-white dark:bg-gray-800
         rounded border p-2 overflow-auto max-h-32;
}

.test-input-container {
  @apply border border-gray-200 dark:border-gray-700 rounded-lg p-4;
}

.test-messages {
  @apply space-y-4 max-h-96 overflow-y-auto;
}

.layout-info {
  @apply bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4;
}

.layout-info p {
  @apply text-sm text-gray-600 dark:text-gray-300 mb-1;
}

.layout-controls,
.state-controls {
  @apply flex flex-wrap gap-2;
}

.test-btn {
  @apply px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg
         transition-colors duration-200 text-sm font-medium;
}

.test-btn.active {
  @apply bg-green-500 hover:bg-green-600;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .test-container {
    @apply px-2;
  }

  .test-section {
    @apply p-4;
  }

  .layout-controls,
  .state-controls {
    @apply flex-col;
  }

  .test-btn {
    @apply w-full;
  }
}
</style>
