<template>
  <svg
    class="bot-icon"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect x="3" y="11" width="18" height="10" rx="2" stroke="currentColor" stroke-width="2"/>
    <circle cx="12" cy="2" r="1" stroke="currentColor" stroke-width="2"/>
    <path d="M12 3v8" stroke="currentColor" stroke-width="2"/>
    <circle cx="8" cy="16" r="1" fill="currentColor"/>
    <circle cx="16" cy="16" r="1" fill="currentColor"/>
  </svg>
</template>

<script setup lang="ts">
interface Props {
  size?: number
}

const props = withDefaults(defineProps<Props>(), {
  size: 20
})
</script>

<style scoped>
.bot-icon {
  width: v-bind(props.size + 'px');
  height: v-bind(props.size + 'px');
}
</style>
