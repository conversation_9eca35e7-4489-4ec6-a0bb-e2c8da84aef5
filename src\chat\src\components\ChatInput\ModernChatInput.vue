<template>
  <div class="modern-chat-input-wrapper">
    <!-- 文件预览区域 -->
    <div v-if="hasFiles || selectedApp" class="file-preview-area chat-fade-in">
      <!-- 文件预览 -->
      <div v-if="uploadedFiles.length > 0" class="file-preview-grid">
        <div
          v-for="(file, index) in uploadedFiles"
          :key="index"
          class="file-preview-item"
        >
          <img
            v-if="file.type.startsWith('image/')"
            :src="file.preview"
            :alt="file.name"
            class="file-preview-image"
          />
          <div v-else class="file-preview-doc">
            <FileIcon class="file-icon" />
            <span class="file-name">{{ file.name }}</span>
          </div>
          <button
            @click="removeFile(index)"
            class="file-remove-btn chat-btn-icon"
            :aria-label="`删除文件 ${file.name}`"
          >
            <CloseIcon />
          </button>
        </div>
      </div>

      <!-- 应用选择预览 -->
      <div v-if="selectedApp" class="app-preview">
        <div class="app-info">
          <div class="app-avatar">
            <img v-if="selectedApp.avatar" :src="selectedApp.avatar" :alt="selectedApp.name" />
            <div v-else class="app-avatar-fallback">{{ selectedApp.name.charAt(0) }}</div>
          </div>
          <div class="app-details">
            <h4 class="app-name">{{ selectedApp.name }}</h4>
            <p class="app-description">{{ selectedApp.description }}</p>
          </div>
        </div>
        <button
          @click="clearSelectedApp"
          class="app-remove-btn chat-btn-icon"
          aria-label="取消选择应用"
        >
          <CloseIcon />
        </button>
      </div>
    </div>

    <!-- 主输入区域 -->
    <div class="chat-input-container" :class="{ 'has-files': hasFiles }">
      <!-- 装饰性背景元素 -->
      <div class="input-decoration">
        <div class="decoration-dot decoration-dot-1"></div>
        <div class="decoration-dot decoration-dot-2"></div>
      </div>

      <div class="input-content">
        <!-- 左侧工具按钮 -->
        <div class="input-tools-left">
          <button
            v-if="showFileUpload"
            @click="triggerFileUpload"
            class="tool-btn chat-btn-icon"
            :disabled="isUploading"
            aria-label="上传文件"
          >
            <LoadingIcon v-if="isUploading" class="animate-spin" />
            <AttachIcon v-else />
          </button>

          <button
            v-if="showModelSelector"
            @click="toggleModelSelector"
            class="tool-btn chat-btn-icon"
            aria-label="选择模型"
          >
            <BotIcon />
          </button>
        </div>

        <!-- 输入框区域 -->
        <div class="textarea-wrapper">
          <textarea
            ref="textareaRef"
            v-model="inputValue"
            :placeholder="placeholder"
            :disabled="disabled"
            class="chat-input-textarea"
            :style="textareaStyle"
            @input="handleInput"
            @keydown="handleKeydown"
            @paste="handlePaste"
            @focus="handleFocus"
            @blur="handleBlur"
            :aria-label="placeholder"
            :aria-describedby="errorMessage ? 'input-error' : undefined"
          />

          <!-- 字符计数 -->
          <div v-if="showCharCount" class="char-count">
            {{ inputValue.length }}{{ maxLength ? `/${maxLength}` : '' }}
          </div>
        </div>

        <!-- 右侧操作按钮 -->
        <div class="input-actions">
          <button
            v-if="!isStreaming && canSend"
            @click="handleSend"
            class="send-btn chat-btn-primary"
            :disabled="!canSend"
            aria-label="发送消息"
          >
            <SendIcon />
          </button>

          <button
            v-if="isStreaming"
            @click="handleStop"
            class="stop-btn"
            aria-label="停止生成"
          >
            <StopIcon />
          </button>
        </div>
      </div>

      <!-- 错误提示 -->
      <div v-if="errorMessage" id="input-error" class="error-message chat-fade-in">
        {{ errorMessage }}
      </div>
    </div>

    <!-- 建议列表 -->
    <div v-if="showSuggestions && suggestions.length > 0" class="suggestions-list chat-slide-up">
      <div
        v-for="(suggestion, index) in suggestions"
        :key="index"
        @click="selectSuggestion(suggestion)"
        class="suggestion-item"
        :class="{ active: index === selectedSuggestionIndex }"
      >
        <div class="suggestion-icon">
          <BotIcon v-if="suggestion.type === 'model'" />
          <span v-else>{{ suggestion.icon }}</span>
        </div>
        <div class="suggestion-content">
          <div class="suggestion-title">{{ suggestion.title }}</div>
          <div class="suggestion-description">{{ suggestion.description }}</div>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      multiple
      class="hidden"
      :accept="acceptedFileTypes"
      @change="handleFileSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { useFileUpload } from '@/hooks/useFileUpload'
import { useSuggestions } from '@/hooks/useSuggestions'

// 图标组件
import FileIcon from './icons/FileIcon.vue'
import CloseIcon from './icons/CloseIcon.vue'
import LoadingIcon from './icons/LoadingIcon.vue'
import AttachIcon from './icons/AttachIcon.vue'
import SendIcon from './icons/SendIcon.vue'
import StopIcon from './icons/StopIcon.vue'
import BotIcon from './icons/BotIcon.vue'

interface Props {
  modelValue: string
  placeholder?: string
  disabled?: boolean
  showFileUpload?: boolean
  showModelSelector?: boolean
  showCharCount?: boolean
  maxLength?: number
  minRows?: number
  maxRows?: number
  acceptedFileTypes?: string
  isStreaming?: boolean
  selectedApp?: any
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'send', data: { message: string; files: File[]; app: any }): void
  (e: 'stop'): void
  (e: 'file-upload', files: File[]): void
  (e: 'app-select', app: any): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入消息...',
  disabled: false,
  showFileUpload: true,
  showModelSelector: true,
  showCharCount: false,
  minRows: 1,
  maxRows: 8,
  acceptedFileTypes: 'image/*,.pdf,.txt,.docx,.pptx,.xlsx,.xml,.js,.json,.sql',
  isStreaming: false
})

const emit = defineEmits<Emits>()

// 基础设置
const { isMobile } = useBasicLayout()
const textareaRef = ref<HTMLTextAreaElement>()
const fileInputRef = ref<HTMLInputElement>()

// 状态管理
const inputValue = ref(props.modelValue)
const errorMessage = ref('')
const isUploading = ref(false)
const showModelSelector = ref(false)

// 文件上传
const {
  uploadedFiles,
  uploadFile,
  removeFile,
  clearFiles
} = useFileUpload()

// 建议系统
const {
  suggestions,
  showSuggestions,
  selectedSuggestionIndex,
  searchSuggestions,
  selectSuggestion,
  clearSuggestions
} = useSuggestions()

// 计算属性
const hasFiles = computed(() => uploadedFiles.value.length > 0 || !!props.selectedApp)

const canSend = computed(() => {
  return !props.disabled &&
         !props.isStreaming &&
         (inputValue.value.trim() || uploadedFiles.value.length > 0)
})

const textareaStyle = computed(() => {
  const lineHeight = 1.5
  const minHeight = props.minRows * lineHeight
  const maxHeight = props.maxRows * lineHeight

  return {
    minHeight: `${minHeight}rem`,
    maxHeight: `${maxHeight}rem`,
    lineHeight: lineHeight
  }
})

// 双向绑定
watch(() => props.modelValue, (newValue) => {
  inputValue.value = newValue
})

watch(inputValue, (newValue) => {
  emit('update:modelValue', newValue)
})

// 方法
const autoResize = () => {
  if (!textareaRef.value) return

  const textarea = textareaRef.value
  textarea.style.height = 'auto'

  const scrollHeight = textarea.scrollHeight
  const lineHeight = parseFloat(getComputedStyle(textarea).lineHeight) || 24
  const maxHeight = lineHeight * props.maxRows

  textarea.style.height = Math.min(scrollHeight, maxHeight) + 'px'
  textarea.style.overflowY = scrollHeight > maxHeight ? 'auto' : 'hidden'
}

const handleInput = (event: Event) => {
  const target = event.target as HTMLTextAreaElement
  inputValue.value = target.value

  nextTick(() => autoResize())

  // 处理@符号建议
  if (inputValue.value.includes('@')) {
    const atIndex = inputValue.value.lastIndexOf('@')
    const searchTerm = inputValue.value.slice(atIndex + 1)
    searchSuggestions(searchTerm)
  } else {
    clearSuggestions()
  }
}

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    handleSend()
  }

  // 处理建议选择
  if (showSuggestions.value && suggestions.value.length > 0) {
    if (event.key === 'ArrowUp') {
      event.preventDefault()
      // 处理向上箭头
    } else if (event.key === 'ArrowDown') {
      event.preventDefault()
      // 处理向下箭头
    } else if (event.key === 'Tab' || event.key === 'Enter') {
      event.preventDefault()
      selectSuggestion(suggestions.value[selectedSuggestionIndex.value])
    }
  }
}

const handlePaste = (event: ClipboardEvent) => {
  const items = event.clipboardData?.items
  if (!items) return

  for (const item of items) {
    if (item.type.startsWith('image/')) {
      event.preventDefault()
      const file = item.getAsFile()
      if (file) {
        uploadFile(file)
      }
    }
  }
}

const handleFocus = () => {
  errorMessage.value = ''
}

const handleBlur = () => {
  // 延迟隐藏建议，允许点击建议项
  setTimeout(() => {
    clearSuggestions()
  }, 200)
}

const handleSend = () => {
  if (!canSend.value) return

  const message = inputValue.value.trim()
  if (!message && uploadedFiles.value.length === 0) {
    errorMessage.value = '请输入消息内容或上传文件'
    return
  }

  emit('send', {
    message,
    files: uploadedFiles.value,
    app: props.selectedApp
  })

  // 清空输入
  inputValue.value = ''
  clearFiles()
  clearSuggestions()

  nextTick(() => autoResize())
}

const handleStop = () => {
  emit('stop')
}

const triggerFileUpload = () => {
  fileInputRef.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (!files) return

  for (const file of files) {
    uploadFile(file)
  }

  // 清空input值，允许重复选择同一文件
  target.value = ''
}

const toggleModelSelector = () => {
  showModelSelector.value = !showModelSelector.value
}

const clearSelectedApp = () => {
  emit('app-select', null)
}

// 初始化
nextTick(() => autoResize())
</script>

<style scoped>
.modern-chat-input-wrapper {
  position: relative;
  width: 100%;
  max-width: 64rem;
  margin: 0 auto;
  padding: 0 1rem;
}

.file-preview-area {
  margin-bottom: 1rem;
  padding: 1rem;
  background: var(--chat-bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--chat-border-light);
}

.file-preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.file-preview-item {
  position: relative;
  border-radius: var(--radius-md);
  overflow: hidden;
  background: var(--chat-bg-primary);
  border: 1px solid var(--chat-border-light);
  transition: all var(--chat-transition-normal) var(--chat-ease-out);
}

.file-preview-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.file-preview-image {
  width: 100%;
  height: 80px;
  object-fit: cover;
}

.file-preview-doc {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  height: 80px;
  text-align: center;
}

.file-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: var(--chat-text-secondary);
  margin-bottom: 0.25rem;
}

.file-name {
  font-size: 0.75rem;
  color: var(--chat-text-secondary);
  word-break: break-all;
  line-height: 1.2;
}

.file-remove-btn {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  width: 1.5rem;
  height: 1.5rem;
  padding: 0;
  background: rgba(239, 68, 68, 0.9);
  color: white;
  border: none;
  opacity: 0;
  transition: all var(--chat-transition-fast) var(--chat-ease-out);
}

.file-preview-item:hover .file-remove-btn {
  opacity: 1;
}

.app-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  background: var(--chat-bg-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--chat-border-light);
}

.app-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.app-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: var(--radius-md);
  overflow: hidden;
  border: 1px solid var(--chat-border-light);
}

.app-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.app-avatar-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--chat-btn-primary-bg);
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
}

.app-details {
  flex: 1;
  min-width: 0;
}

.app-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--chat-text-primary);
  margin: 0 0 0.25rem 0;
}

.app-description {
  font-size: 0.75rem;
  color: var(--chat-text-secondary);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.app-remove-btn {
  width: 1.5rem;
  height: 1.5rem;
  padding: 0;
  flex-shrink: 0;
}

.input-decoration {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  pointer-events: none;
  overflow: hidden;
  border-radius: inherit;
}

.decoration-dot {
  position: absolute;
  width: 2px;
  height: 2px;
  border-radius: 50%;
  background: var(--chat-input-focus-border);
  opacity: 0.3;
}

.decoration-dot-1 {
  top: 0.5rem;
  right: 0.5rem;
  animation: pulse 2s ease-in-out infinite;
}

.decoration-dot-2 {
  bottom: 0.5rem;
  left: 0.5rem;
  animation: pulse 2s ease-in-out infinite 1s;
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.2); }
}

.input-content {
  display: flex;
  align-items: flex-end;
  gap: 0.75rem;
  padding: 0.75rem;
  position: relative;
  z-index: 1;
}

.input-tools-left {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.tool-btn {
  width: 2.25rem;
  height: 2.25rem;
  padding: 0;
  flex-shrink: 0;
}

.textarea-wrapper {
  flex: 1;
  position: relative;
  min-width: 0;
}

.char-count {
  position: absolute;
  bottom: 0.25rem;
  right: 0.5rem;
  font-size: 0.75rem;
  color: var(--chat-text-muted);
  background: var(--chat-bg-primary);
  padding: 0.125rem 0.25rem;
  border-radius: var(--radius-sm);
  pointer-events: none;
}

.input-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.send-btn {
  width: 2.5rem;
  height: 2.5rem;
  padding: 0;
  border-radius: var(--radius-full);
  flex-shrink: 0;
}

.stop-btn {
  width: 2.5rem;
  height: 2.5rem;
  padding: 0;
  border-radius: var(--radius-full);
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  cursor: pointer;
  transition: all var(--chat-transition-normal) var(--chat-ease-out);
  flex-shrink: 0;
}

.stop-btn:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.error-message {
  padding: 0.5rem 0.75rem;
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  font-size: 0.875rem;
  border-radius: var(--radius-md);
  margin-top: 0.5rem;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.suggestions-list {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: var(--chat-bg-primary);
  border: 1px solid var(--chat-border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  max-height: 200px;
  overflow-y: auto;
  z-index: 50;
  margin-bottom: 0.5rem;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  cursor: pointer;
  transition: all var(--chat-transition-fast) var(--chat-ease-out);
  border-bottom: 1px solid var(--chat-border-light);
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover,
.suggestion-item.active {
  background: var(--chat-bg-secondary);
}

.suggestion-icon {
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--chat-text-secondary);
  flex-shrink: 0;
}

.suggestion-content {
  flex: 1;
  min-width: 0;
}

.suggestion-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--chat-text-primary);
  margin-bottom: 0.125rem;
}

.suggestion-description {
  font-size: 0.75rem;
  color: var(--chat-text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.hidden {
  display: none;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-chat-input-wrapper {
    padding: 0 0.5rem;
  }

  .file-preview-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.5rem;
  }

  .input-content {
    gap: 0.5rem;
    padding: 0.5rem;
  }

  .tool-btn {
    width: 2rem;
    height: 2rem;
  }

  .send-btn,
  .stop-btn {
    width: 2.25rem;
    height: 2.25rem;
  }
}

/* 无障碍性增强 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .chat-input-container {
    border-width: 2px;
  }

  .file-preview-item,
  .app-preview {
    border-width: 2px;
  }
}
</style>
