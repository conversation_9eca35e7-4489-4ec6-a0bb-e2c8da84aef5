<template>
  <header class="modern-navbar" :class="{ 'scrolled': scrolled }">
    <div class="navbar-container">
      <!-- Logo区域 -->
      <div class="logo-container">
        <div class="logo-wrapper">
          <img :src="logoPath" alt="Logo" class="logo" />
          <div class="logo-glow"></div>
        </div>
        <h1 class="site-name">{{ siteName }}</h1>
      </div>

      <!-- 导航链接 -->
      <nav class="nav-links" :class="{ 'mobile-open': mobileMenuOpen }">
        <a href="#features" class="nav-link" @click="closeMobileMenu">
          <span class="nav-link-text">创作类型</span>
          <div class="nav-link-indicator"></div>
        </a>
        <a href="#community" class="nav-link" @click="closeMobileMenu">
          <span class="nav-link-text">社区作品</span>
          <div class="nav-link-indicator"></div>
        </a>
        <a href="#about" class="nav-link" @click="closeMobileMenu">
          <span class="nav-link-text">关于我们</span>
          <div class="nav-link-indicator"></div>
        </a>
      </nav>

      <!-- 右侧操作区域 -->
      <div class="nav-actions">
        <!-- 主题切换按钮 -->
        <button
          @click="toggleTheme"
          class="theme-toggle-btn"
          :aria-label="isDark ? '切换到亮色主题' : '切换到深色主题'"
          title="切换主题"
        >
          <SunIcon v-if="isDark" />
          <MoonIcon v-else />
        </button>

        <!-- 登录/用户菜单 -->
        <div v-if="!isLogin" class="login-section">
          <button @click="handleLogin" class="login-btn chat-btn-primary">
            <span>登录 / 注册</span>
            <div class="btn-shine"></div>
          </button>
        </div>

        <div v-else class="user-menu">
          <n-dropdown
            :options="userMenuOptions"
            @select="handleUserMenuSelect"
            placement="bottom-end"
            trigger="click"
          >
            <div class="user-avatar-container">
              <div class="user-avatar chat-avatar">
                <img v-if="avatar && !avatarError" :src="avatar" alt="用户头像" @error="handleAvatarError" />
                <div v-else class="avatar-placeholder">{{ userInitial }}</div>
              </div>
              <div class="user-status-indicator"></div>
            </div>
          </n-dropdown>
        </div>

        <!-- 移动端菜单按钮 -->
        <button
          @click="toggleMobileMenu"
          class="mobile-menu-btn"
          :aria-label="mobileMenuOpen ? '关闭菜单' : '打开菜单'"
        >
          <div class="hamburger" :class="{ active: mobileMenuOpen }">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </button>
      </div>
    </div>

    <!-- 移动端菜单遮罩 -->
    <div
      v-if="mobileMenuOpen"
      class="mobile-menu-overlay"
      @click="closeMobileMenu"
    ></div>
  </header>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import { NDropdown } from 'naive-ui';
import { useAuthStore, useGlobalStoreWithOut } from '@/store';
import logo from '@/assets/logo.png';

// 图标组件
import SunIcon from './icons/SunIcon.vue'
import MoonIcon from './icons/MoonIcon.vue'

const useGlobalStore = useGlobalStoreWithOut();

const props = defineProps({
  scrolled: {
    type: Boolean,
    default: false
  }
});

const router = useRouter();
const authStore = useAuthStore();

// 状态管理
const mobileMenuOpen = ref(false)
const avatarError = ref(false)
const isDark = ref(false) // 这里应该从主题store获取

// 计算属性
const isLogin = computed(() => authStore.isLogin);
const avatar = computed(() => authStore.userInfo.avatar);
const siteName = computed(() => authStore.globalConfig?.siteName || 'DeepCreate');
const logoPath = computed(() => authStore.globalConfig.clientLogoPath || logo);

const userInitial = computed(() => {
  const username = authStore.userInfo.username || '';
  return username.charAt(0).toUpperCase();
});

// 用户菜单选项
const userMenuOptions = computed(() => [
  {
    label: '个人中心',
    key: 'user-center',
    icon: '👤'
  },
  {
    label: '我的作品',
    key: 'my-works',
    icon: '📁'
  },
  {
    label: '设置',
    key: 'settings',
    icon: '⚙️'
  },
  {
    type: 'divider'
  },
  {
    label: '退出登录',
    key: 'logout',
    icon: '🚪'
  }
]);

// 方法
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

const toggleTheme = () => {
  isDark.value = !isDark.value
  // 这里应该调用主题store的方法
  document.documentElement.classList.toggle('dark')
}

const handleAvatarError = () => {
  avatarError.value = true
}

// 处理登录按钮点击
function handleLogin() {
  authStore.setLoginDialog(true);
}

// 处理用户菜单选择
function handleUserMenuSelect(key: string) {
  switch (key) {
    case 'user-center':
      // 使用弹窗而不是页面跳转
      useGlobalStore.updateUserCenterDialog(true);
      break;
    case 'my-works':
      router.push('/works');
      break;
    case 'settings':
      // 打开设置页面或弹窗
      break;
    case 'logout':
      authStore.logOut();
      break;
  }
}
</script>

<style scoped>
.modern-navbar {
  position: sticky;
  top: 0;
  z-index: 100;
  background: var(--chat-bg-primary);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--chat-border-light);
  transition: all var(--chat-transition-normal) var(--chat-ease-out);
  padding: 0.75rem 0;
}

.modern-navbar.scrolled {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: var(--shadow-lg);
  padding: 0.5rem 0;
}

.dark .modern-navbar {
  background: var(--chat-bg-primary);
  border-bottom-color: var(--chat-border-light);
}

.dark .modern-navbar.scrolled {
  background: rgba(15, 23, 42, 0.95);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  z-index: 10;
}

.logo-wrapper {
  position: relative;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all var(--chat-transition-normal) var(--chat-ease-out);
}

.logo-wrapper:hover {
  transform: scale(1.05);
}

.logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
  position: relative;
  z-index: 2;
}

.logo-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--chat-btn-primary-bg);
  opacity: 0;
  transition: opacity var(--chat-transition-normal) var(--chat-ease-out);
  border-radius: inherit;
}

.logo-wrapper:hover .logo-glow {
  opacity: 0.1;
}

.site-name {
  font-size: 1.375rem;
  font-weight: 700;
  color: var(--chat-text-primary);
  margin: 0;
  background: var(--chat-btn-primary-bg);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  transition: all var(--chat-transition-normal) var(--chat-ease-out);
}

.nav-links {
  display: flex;
  gap: 2rem;
  align-items: center;
  transition: all var(--chat-transition-normal) var(--chat-ease-out);
}

.nav-link {
  position: relative;
  text-decoration: none;
  padding: 0.5rem 0;
  transition: all var(--chat-transition-normal) var(--chat-ease-out);
  cursor: pointer;
}

.nav-link-text {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--chat-text-secondary);
  transition: color var(--chat-transition-fast) var(--chat-ease-out);
}

.nav-link:hover .nav-link-text {
  color: var(--chat-text-primary);
}

.nav-link-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--chat-btn-primary-bg);
  border-radius: 1px;
  transition: width var(--chat-transition-normal) var(--chat-ease-out);
}

.nav-link:hover .nav-link-indicator {
  width: 100%;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  z-index: 10;
}

.theme-toggle-btn {
  width: 2.25rem;
  height: 2.25rem;
  border: none;
  border-radius: var(--radius-full);
  background: var(--chat-btn-secondary-bg);
  color: var(--chat-text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--chat-transition-normal) var(--chat-ease-out);
  position: relative;
  overflow: hidden;
}

.theme-toggle-btn:hover {
  background: var(--chat-btn-secondary-hover);
  color: var(--chat-text-primary);
  transform: scale(1.05);
}

.login-section {
  position: relative;
}

.login-btn {
  position: relative;
  padding: 0.5rem 1.25rem;
  font-weight: 500;
  font-size: 0.875rem;
  border: none;
  cursor: pointer;
  overflow: hidden;
  transition: all var(--chat-transition-normal) var(--chat-ease-out);
}

.btn-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left var(--chat-transition-slow) var(--chat-ease-out);
}

.login-btn:hover .btn-shine {
  left: 100%;
}

.user-menu {
  position: relative;
}

.user-avatar-container {
  position: relative;
  cursor: pointer;
}

.user-status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 0.75rem;
  height: 0.75rem;
  background: #10b981;
  border: 2px solid var(--chat-bg-primary);
  border-radius: 50%;
  transition: all var(--chat-transition-normal) var(--chat-ease-out);
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--chat-btn-primary-bg);
  color: white;
  font-weight: 600;
  font-size: 1rem;
}

.mobile-menu-btn {
  display: none;
  width: 2.25rem;
  height: 2.25rem;
  border: none;
  background: transparent;
  cursor: pointer;
  padding: 0;
  align-items: center;
  justify-content: center;
}

.hamburger {
  width: 1.25rem;
  height: 1rem;
  position: relative;
  transition: all var(--chat-transition-normal) var(--chat-ease-out);
}

.hamburger span {
  display: block;
  position: absolute;
  height: 2px;
  width: 100%;
  background: var(--chat-text-primary);
  border-radius: 1px;
  opacity: 1;
  left: 0;
  transition: all var(--chat-transition-normal) var(--chat-ease-out);
}

.hamburger span:nth-child(1) {
  top: 0;
}

.hamburger span:nth-child(2) {
  top: 50%;
  transform: translateY(-50%);
}

.hamburger span:nth-child(3) {
  bottom: 0;
}

.hamburger.active span:nth-child(1) {
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
}

.hamburger.active span:nth-child(2) {
  opacity: 0;
}

.hamburger.active span:nth-child(3) {
  bottom: 50%;
  transform: translateY(50%) rotate(-45deg);
}

.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 99;
  opacity: 0;
  animation: fadeIn var(--chat-transition-normal) var(--chat-ease-out) forwards;
}

@keyframes fadeIn {
  to { opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-links {
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--chat-bg-primary);
    border-top: 1px solid var(--chat-border-light);
    flex-direction: column;
    gap: 0;
    padding: 1rem 0;
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--chat-transition-normal) var(--chat-ease-out);
    z-index: 100;
    box-shadow: var(--shadow-xl);
  }

  .nav-links.mobile-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav-link {
    padding: 0.75rem 1rem;
    width: 100%;
    text-align: center;
  }

  .nav-link-indicator {
    left: 50%;
    transform: translateX(-50%);
  }

  .mobile-menu-btn {
    display: flex;
  }

  .theme-toggle-btn {
    width: 2rem;
    height: 2rem;
  }

  .login-btn {
    padding: 0.375rem 1rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .navbar-container {
    padding: 0 0.75rem;
  }

  .site-name {
    font-size: 1.125rem;
  }

  .nav-actions {
    gap: 0.5rem;
  }
}

/* 无障碍性增强 */
@media (prefers-reduced-motion: reduce) {
  .modern-navbar,
  .logo-wrapper,
  .nav-link,
  .theme-toggle-btn,
  .login-btn,
  .hamburger,
  .hamburger span {
    transition: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .modern-navbar {
    border-bottom-width: 2px;
  }

  .nav-link-indicator {
    height: 3px;
  }
}
</style>
