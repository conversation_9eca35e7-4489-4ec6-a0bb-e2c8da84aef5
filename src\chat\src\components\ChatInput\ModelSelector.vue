<template>
  <div class="model-selector" ref="selectorRef">
    <button
      @click="toggleDropdown"
      class="model-selector-btn"
      :class="{ active: isOpen }"
      :aria-expanded="isOpen"
      :aria-haspopup="true"
      :aria-label="`当前模型: ${currentModel?.name || '未选择'}`"
    >
      <div class="model-info">
        <img 
          v-if="currentModel?.avatar" 
          :src="currentModel.avatar" 
          :alt="currentModel.name"
          class="model-avatar"
        />
        <div v-else class="model-avatar-fallback">
          <BotIcon />
        </div>
        <div class="model-details">
          <span class="model-name">{{ currentModel?.name || '选择模型' }}</span>
          <span v-if="currentModel?.description" class="model-desc">{{ currentModel.description }}</span>
        </div>
      </div>
      <ChevronDownIcon :class="{ 'rotate-180': isOpen }" class="transition-transform duration-200" />
    </button>

    <!-- 下拉菜单 -->
    <div 
      v-if="isOpen"
      class="model-dropdown"
      role="listbox"
      :aria-label="'选择AI模型'"
    >
      <div class="dropdown-header">
        <h3 class="dropdown-title">选择AI模型</h3>
        <p class="dropdown-subtitle">选择最适合您需求的AI模型</p>
      </div>

      <div class="model-categories">
        <div 
          v-for="category in modelCategories" 
          :key="category.name"
          class="model-category"
        >
          <h4 class="category-title">{{ category.name }}</h4>
          <div class="category-models">
            <button
              v-for="model in category.models"
              :key="model.id"
              @click="selectModel(model)"
              class="model-option"
              :class="{ 
                active: currentModel?.id === model.id,
                disabled: !model.available 
              }"
              :disabled="!model.available"
              role="option"
              :aria-selected="currentModel?.id === model.id"
            >
              <div class="model-option-content">
                <div class="model-option-header">
                  <img 
                    v-if="model.avatar" 
                    :src="model.avatar" 
                    :alt="model.name"
                    class="model-option-avatar"
                  />
                  <div class="model-option-info">
                    <span class="model-option-name">{{ model.name }}</span>
                    <div class="model-option-meta">
                      <span v-if="model.provider" class="model-provider">{{ model.provider }}</span>
                      <span v-if="model.version" class="model-version">v{{ model.version }}</span>
                    </div>
                  </div>
                  <div class="model-option-status">
                    <span v-if="!model.available" class="status-unavailable">不可用</span>
                    <span v-else-if="model.isPremium" class="status-premium">Pro</span>
                    <CheckIcon v-else-if="currentModel?.id === model.id" class="status-selected" />
                  </div>
                </div>
                
                <p v-if="model.description" class="model-option-desc">{{ model.description }}</p>
                
                <div v-if="model.capabilities" class="model-capabilities">
                  <span 
                    v-for="capability in model.capabilities" 
                    :key="capability"
                    class="capability-tag"
                  >
                    {{ capability }}
                  </span>
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>

      <div class="dropdown-footer">
        <button @click="closeDropdown" class="close-btn">
          关闭
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { onClickOutside } from '@vueuse/core'

// 图标组件
import BotIcon from './icons/BotIcon.vue'
import ChevronDownIcon from './icons/ChevronDownIcon.vue'
import CheckIcon from './icons/CheckIcon.vue'

interface Model {
  id: string
  name: string
  description?: string
  avatar?: string
  provider?: string
  version?: string
  available: boolean
  isPremium?: boolean
  capabilities?: string[]
  type?: 'text' | 'image' | 'video' | 'audio'
}

interface ModelCategory {
  name: string
  models: Model[]
}

interface Props {
  currentModel?: Model | null
  models?: Model[]
}

interface Emits {
  (e: 'change', model: Model): void
}

const props = withDefaults(defineProps<Props>(), {
  currentModel: null,
  models: () => []
})

const emit = defineEmits<Emits>()

// 状态管理
const isOpen = ref(false)
const selectorRef = ref<HTMLElement>()

// 模型分类
const modelCategories = computed<ModelCategory[]>(() => {
  const categories: ModelCategory[] = [
    {
      name: '文本生成',
      models: props.models.filter(m => !m.type || m.type === 'text')
    },
    {
      name: '图像生成',
      models: props.models.filter(m => m.type === 'image')
    },
    {
      name: '视频生成',
      models: props.models.filter(m => m.type === 'video')
    },
    {
      name: '音频生成',
      models: props.models.filter(m => m.type === 'audio')
    }
  ]
  
  return categories.filter(category => category.models.length > 0)
})

// 事件处理
const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

const closeDropdown = () => {
  isOpen.value = false
}

const selectModel = (model: Model) => {
  if (!model.available) return
  
  emit('change', model)
  closeDropdown()
}

// 点击外部关闭
onClickOutside(selectorRef, () => {
  if (isOpen.value) {
    closeDropdown()
  }
})

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isOpen.value) {
    closeDropdown()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.model-selector {
  @apply relative;
}

.model-selector-btn {
  @apply flex items-center gap-3 p-3 bg-white dark:bg-gray-800 
         border border-gray-200 dark:border-gray-700 rounded-lg
         hover:border-blue-300 dark:hover:border-blue-600
         transition-all duration-200 min-w-48;
}

.model-selector-btn.active {
  @apply border-blue-500 dark:border-blue-400 shadow-sm;
}

.model-info {
  @apply flex items-center gap-2 flex-1 min-w-0;
}

.model-avatar {
  @apply w-6 h-6 rounded-full object-cover;
}

.model-avatar-fallback {
  @apply w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-700 
         flex items-center justify-center text-gray-500 dark:text-gray-400;
}

.model-details {
  @apply flex flex-col min-w-0;
}

.model-name {
  @apply text-sm font-medium text-gray-900 dark:text-gray-100 truncate;
}

.model-desc {
  @apply text-xs text-gray-500 dark:text-gray-400 truncate;
}

/* 下拉菜单 */
.model-dropdown {
  @apply absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 
         border border-gray-200 dark:border-gray-700 rounded-xl shadow-xl 
         z-50 max-h-96 overflow-y-auto;
}

.dropdown-header {
  @apply p-4 border-b border-gray-200 dark:border-gray-700;
}

.dropdown-title {
  @apply text-lg font-semibold text-gray-900 dark:text-gray-100;
}

.dropdown-subtitle {
  @apply text-sm text-gray-500 dark:text-gray-400 mt-1;
}

/* 模型分类 */
.model-categories {
  @apply p-2;
}

.model-category {
  @apply mb-4 last:mb-0;
}

.category-title {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300 
         px-2 py-1 mb-2;
}

.category-models {
  @apply space-y-1;
}

/* 模型选项 */
.model-option {
  @apply w-full p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700
         transition-colors duration-200 text-left;
}

.model-option.active {
  @apply bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700;
}

.model-option.disabled {
  @apply opacity-50 cursor-not-allowed;
}

.model-option-content {
  @apply space-y-2;
}

.model-option-header {
  @apply flex items-start gap-3;
}

.model-option-avatar {
  @apply w-8 h-8 rounded-full object-cover flex-shrink-0;
}

.model-option-info {
  @apply flex-1 min-w-0;
}

.model-option-name {
  @apply text-sm font-medium text-gray-900 dark:text-gray-100;
}

.model-option-meta {
  @apply flex items-center gap-2 mt-1;
}

.model-provider {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.model-version {
  @apply text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 
         px-1.5 py-0.5 rounded;
}

.model-option-status {
  @apply flex-shrink-0;
}

.status-unavailable {
  @apply text-xs text-red-500 dark:text-red-400;
}

.status-premium {
  @apply text-xs bg-gradient-to-r from-yellow-400 to-orange-500 
         text-white px-2 py-1 rounded-full font-medium;
}

.status-selected {
  @apply w-5 h-5 text-blue-500 dark:text-blue-400;
}

.model-option-desc {
  @apply text-xs text-gray-600 dark:text-gray-400 leading-relaxed;
}

/* 能力标签 */
.model-capabilities {
  @apply flex flex-wrap gap-1;
}

.capability-tag {
  @apply text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 
         px-2 py-1 rounded-full;
}

/* 底部 */
.dropdown-footer {
  @apply p-3 border-t border-gray-200 dark:border-gray-700;
}

.close-btn {
  @apply w-full py-2 text-sm text-gray-600 dark:text-gray-400 
         hover:text-gray-900 dark:hover:text-gray-100 transition-colors duration-200;
}

/* 滚动条 */
.model-dropdown::-webkit-scrollbar {
  @apply w-2;
}

.model-dropdown::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

.model-dropdown::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}
</style>
