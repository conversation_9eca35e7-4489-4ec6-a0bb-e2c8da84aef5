<template>
  <div
    class="modern-chat-message"
    :class="messageClasses"
    :data-message-id="messageId"
    role="article"
    :aria-label="messageAriaLabel"
  >
    <!-- 消息容器 -->
    <div class="message-container">
      <!-- 头像 -->
      <div class="avatar-container">
        <div class="chat-avatar" :class="{ 'user-avatar': isUser, 'bot-avatar': !isUser }">
          <img
            v-if="avatar && !avatarError"
            :src="avatar"
            :alt="senderName"
            @error="handleAvatarError"
          />
          <div v-else class="avatar-fallback">
            <UserIcon v-if="isUser" />
            <BotIcon v-else />
          </div>
        </div>
      </div>

      <!-- 消息气泡 -->
      <div class="message-bubble-container">
        <!-- 消息头部信息 -->
        <div v-if="showMessageInfo" class="message-header">
          <span class="sender-name">{{ senderName }}</span>
          <span class="message-time" :title="fullTimestamp">{{ relativeTime }}</span>
        </div>

        <!-- 消息气泡 -->
        <div class="chat-message-bubble" :class="{ 'chat-message-user': isUser, 'chat-message-bot': !isUser }">
          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-container chat-fade-in">
            <div class="chat-loading-dots">
              <div class="chat-loading-dot"></div>
              <div class="chat-loading-dot"></div>
              <div class="chat-loading-dot"></div>
            </div>
            <span class="loading-text">{{ loadingText }}</span>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="hasError" class="error-container chat-fade-in">
            <div class="error-icon">
              <ErrorIcon />
            </div>
            <div class="error-content">
              <div class="error-title">发送失败</div>
              <div class="error-message">{{ errorMessage }}</div>
            </div>
          </div>

          <!-- 正常消息内容 -->
          <div v-else class="message-content chat-fade-in">
            <!-- 附件预览 -->
            <div v-if="attachments.length > 0" class="attachments-container">
              <div
                v-for="(attachment, index) in attachments"
                :key="index"
                class="attachment-item"
                @click="handleAttachmentClick(attachment)"
              >
                <img
                  v-if="attachment.type === 'image'"
                  :src="attachment.url"
                  :alt="attachment.name"
                  class="attachment-image"
                />
                <div v-else class="attachment-file">
                  <FileIcon class="attachment-file-icon" />
                  <span class="attachment-file-name">{{ attachment.name }}</span>
                </div>
              </div>
            </div>

            <!-- 文本内容 -->
            <div
              v-if="content"
              class="text-content"
              :class="{ 'markdown-content': isMarkdown }"
              v-html="formattedContent"
            ></div>

            <!-- 特殊内容类型 -->
            <component
              v-if="specialType"
              :is="specialComponent"
              v-bind="specialProps"
              @action="handleSpecialAction"
            />
          </div>

          <!-- 消息操作按钮 -->
          <div v-if="showActions && !isLoading" class="message-actions">
            <button
              v-if="!isUser"
              @click="handleCopy"
              class="action-btn"
              :aria-label="'复制消息内容'"
              title="复制"
            >
              <CopyIcon />
            </button>

            <button
              v-if="!isUser && canRegenerate"
              @click="handleRegenerate"
              class="action-btn"
              :aria-label="'重新生成'"
              title="重新生成"
            >
              <RefreshIcon />
            </button>

            <button
              v-if="canDelete"
              @click="handleDelete"
              class="action-btn delete-btn"
              :aria-label="'删除消息'"
              title="删除"
            >
              <DeleteIcon />
            </button>

            <button
              v-if="!isUser && showFeedback"
              @click="toggleFeedback"
              class="action-btn"
              :class="{ active: showFeedbackPanel }"
              :aria-label="'反馈'"
              title="反馈"
            >
              <ThumbsUpIcon v-if="feedback === 'positive'" />
              <ThumbsDownIcon v-else-if="feedback === 'negative'" />
              <FeedbackIcon v-else />
            </button>
          </div>
        </div>

        <!-- 反馈面板 -->
        <div v-if="showFeedbackPanel" class="feedback-panel chat-slide-up">
          <div class="feedback-buttons">
            <button
              @click="handleFeedback('positive')"
              class="feedback-btn positive"
              :class="{ active: feedback === 'positive' }"
            >
              <ThumbsUpIcon />
              <span>有用</span>
            </button>
            <button
              @click="handleFeedback('negative')"
              class="feedback-btn negative"
              :class="{ active: feedback === 'negative' }"
            >
              <ThumbsDownIcon />
              <span>无用</span>
            </button>
          </div>
          <textarea
            v-if="feedback"
            v-model="feedbackText"
            placeholder="请提供更多反馈信息（可选）"
            class="feedback-textarea"
            rows="2"
          ></textarea>
          <div class="feedback-actions">
            <button @click="submitFeedback" class="submit-feedback-btn">提交</button>
            <button @click="cancelFeedback" class="cancel-feedback-btn">取消</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

// 图标组件
import UserIcon from './icons/UserIcon.vue'
import BotIcon from './icons/BotIcon.vue'
import CopyIcon from './icons/CopyIcon.vue'
import RefreshIcon from './icons/RefreshIcon.vue'
import DeleteIcon from './icons/DeleteIcon.vue'
import ErrorIcon from './icons/ErrorIcon.vue'
import FileIcon from './icons/FileIcon.vue'
import ThumbsUpIcon from './icons/ThumbsUpIcon.vue'
import ThumbsDownIcon from './icons/ThumbsDownIcon.vue'
import FeedbackIcon from './icons/FeedbackIcon.vue'

interface Attachment {
  type: 'image' | 'file'
  url: string
  name: string
  size?: number
}

interface Props {
  messageId?: string
  content: string
  senderName: string
  avatar?: string
  timestamp: Date
  isUser: boolean
  isLoading?: boolean
  hasError?: boolean
  errorMessage?: string
  loadingText?: string
  attachments?: Attachment[]
  isMarkdown?: boolean
  canRegenerate?: boolean
  canDelete?: boolean
  showFeedback?: boolean
  showMessageInfo?: boolean
  feedback?: 'positive' | 'negative' | null
  specialType?: string
  specialProps?: Record<string, any>
}

interface Emits {
  (e: 'copy'): void
  (e: 'regenerate'): void
  (e: 'delete'): void
  (e: 'feedback', type: 'positive' | 'negative', text?: string): void
  (e: 'attachment-click', attachment: Attachment): void
  (e: 'special-action', action: string, data?: any): void
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  hasError: false,
  errorMessage: '',
  loadingText: '正在思考中...',
  attachments: () => [],
  isMarkdown: true,
  canRegenerate: true,
  canDelete: true,
  showFeedback: true,
  showMessageInfo: true,
  feedback: null,
  specialType: '',
  specialProps: () => ({})
})

const emit = defineEmits<Emits>()

// 状态管理
const avatarError = ref(false)
const showFeedbackPanel = ref(false)
const feedbackText = ref('')

// 计算属性
const messageClasses = computed(() => ({
  'is-user': props.isUser,
  'is-bot': !props.isUser,
  'is-loading': props.isLoading,
  'has-error': props.hasError,
  'has-attachments': props.attachments.length > 0
}))

const messageAriaLabel = computed(() => {
  if (props.isLoading) return `${props.senderName}正在输入`
  if (props.hasError) return `${props.senderName}的消息发送失败`
  return `${props.senderName}在${relativeTime.value}发送的消息`
})

const relativeTime = computed(() => {
  return formatDistanceToNow(props.timestamp, {
    addSuffix: true,
    locale: zhCN
  })
})

const fullTimestamp = computed(() => {
  return props.timestamp.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
})

const formattedContent = computed(() => {
  if (!props.content) return ''

  if (props.isMarkdown) {
    // 这里应该使用markdown解析器
    return props.content.replace(/\n/g, '<br>')
  }

  return props.content.replace(/\n/g, '<br>')
})

const showActions = computed(() => {
  return !props.isLoading && !props.hasError
})

const specialComponent = computed(() => {
  // 根据specialType返回对应的组件
  return null
})

// 方法
const handleAvatarError = () => {
  avatarError.value = true
}

const handleCopy = () => {
  emit('copy')
}

const handleRegenerate = () => {
  emit('regenerate')
}

const handleDelete = () => {
  emit('delete')
}

const handleAttachmentClick = (attachment: Attachment) => {
  emit('attachment-click', attachment)
}

const handleSpecialAction = (action: string, data?: any) => {
  emit('special-action', action, data)
}

const toggleFeedback = () => {
  showFeedbackPanel.value = !showFeedbackPanel.value
}

const handleFeedback = (type: 'positive' | 'negative') => {
  emit('feedback', type, feedbackText.value)
}

const submitFeedback = () => {
  if (props.feedback) {
    emit('feedback', props.feedback, feedbackText.value)
  }
  showFeedbackPanel.value = false
  feedbackText.value = ''
}

const cancelFeedback = () => {
  showFeedbackPanel.value = false
  feedbackText.value = ''
}
</script>

<style scoped>
.modern-chat-message {
  margin: 1rem 0;
  transition: all var(--chat-transition-normal) var(--chat-ease-out);
}

.message-container {
  display: flex;
  gap: 0.75rem;
  align-items: flex-start;
  max-width: 100%;
}

.is-user .message-container {
  flex-direction: row-reverse;
}

.avatar-container {
  flex-shrink: 0;
  position: sticky;
  top: 1rem;
}

.user-avatar {
  background: var(--chat-user-bg);
}

.bot-avatar {
  background: var(--chat-bg-secondary);
  border: 2px solid var(--chat-border-light);
}

.avatar-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
}

.bot-avatar .avatar-fallback {
  color: var(--chat-text-secondary);
}

.message-bubble-container {
  flex: 1;
  min-width: 0;
  max-width: calc(100% - 3rem);
}

.is-user .message-bubble-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
  font-size: 0.75rem;
  color: var(--chat-text-muted);
}

.is-user .message-header {
  flex-direction: row-reverse;
}

.sender-name {
  font-weight: 500;
  color: var(--chat-text-secondary);
}

.message-time {
  color: var(--chat-text-muted);
}

.loading-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
}

.loading-text {
  font-size: 0.875rem;
  color: var(--chat-text-secondary);
  font-style: italic;
}

.error-container {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  padding: 0.5rem 0;
}

.error-icon {
  color: #ef4444;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.error-content {
  flex: 1;
}

.error-title {
  font-weight: 500;
  color: #ef4444;
  margin-bottom: 0.25rem;
}

.error-message {
  font-size: 0.875rem;
  color: var(--chat-text-secondary);
}

.message-content {
  position: relative;
}

.attachments-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.attachment-item {
  border-radius: var(--radius-md);
  overflow: hidden;
  cursor: pointer;
  transition: all var(--chat-transition-normal) var(--chat-ease-out);
  border: 1px solid var(--chat-border-light);
}

.attachment-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.attachment-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.attachment-file {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  height: 120px;
  background: var(--chat-bg-secondary);
  text-align: center;
}

.attachment-file-icon {
  width: 2rem;
  height: 2rem;
  color: var(--chat-text-secondary);
  margin-bottom: 0.5rem;
}

.attachment-file-name {
  font-size: 0.75rem;
  color: var(--chat-text-secondary);
  word-break: break-all;
  line-height: 1.2;
}

.text-content {
  line-height: 1.6;
  word-wrap: break-word;
}

.markdown-content {
  /* Markdown样式将在这里定义 */
}

.message-actions {
  display: flex;
  gap: 0.25rem;
  margin-top: 0.5rem;
  opacity: 0;
  transition: opacity var(--chat-transition-normal) var(--chat-ease-out);
}

.modern-chat-message:hover .message-actions {
  opacity: 1;
}

.action-btn {
  width: 1.75rem;
  height: 1.75rem;
  border: none;
  border-radius: var(--radius-md);
  background: var(--chat-bg-secondary);
  color: var(--chat-text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--chat-transition-fast) var(--chat-ease-out);
  padding: 0;
}

.action-btn:hover {
  background: var(--chat-btn-secondary-hover);
  color: var(--chat-text-primary);
  transform: scale(1.1);
}

.action-btn.active {
  background: var(--chat-input-focus-border);
  color: white;
}

.delete-btn:hover {
  background: #ef4444;
  color: white;
}

.feedback-panel {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: var(--chat-bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--chat-border-light);
}

.feedback-buttons {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.feedback-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--chat-border-light);
  border-radius: var(--radius-md);
  background: var(--chat-bg-primary);
  color: var(--chat-text-secondary);
  cursor: pointer;
  transition: all var(--chat-transition-fast) var(--chat-ease-out);
  font-size: 0.875rem;
}

.feedback-btn:hover {
  background: var(--chat-btn-secondary-hover);
  color: var(--chat-text-primary);
}

.feedback-btn.positive.active {
  background: #10b981;
  color: white;
  border-color: #10b981;
}

.feedback-btn.negative.active {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

.feedback-textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--chat-border-light);
  border-radius: var(--radius-md);
  background: var(--chat-bg-primary);
  color: var(--chat-text-primary);
  font-size: 0.875rem;
  resize: vertical;
  margin-bottom: 0.75rem;
  transition: border-color var(--chat-transition-fast) var(--chat-ease-out);
}

.feedback-textarea:focus {
  outline: none;
  border-color: var(--chat-input-focus-border);
}

.feedback-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.submit-feedback-btn,
.cancel-feedback-btn {
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--chat-transition-fast) var(--chat-ease-out);
}

.submit-feedback-btn {
  background: var(--chat-btn-primary-bg);
  color: white;
  border: none;
}

.submit-feedback-btn:hover {
  background: var(--chat-btn-primary-hover);
}

.cancel-feedback-btn {
  background: transparent;
  color: var(--chat-text-secondary);
  border: 1px solid var(--chat-border-light);
}

.cancel-feedback-btn:hover {
  background: var(--chat-btn-secondary-hover);
  color: var(--chat-text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-bubble-container {
    max-width: calc(100% - 2.5rem);
  }

  .attachments-container {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }

  .attachment-image,
  .attachment-file {
    height: 100px;
  }

  .message-actions {
    opacity: 1; /* 在移动设备上始终显示操作按钮 */
  }

  .feedback-buttons {
    flex-direction: column;
  }

  .feedback-actions {
    flex-direction: column;
  }
}

/* 无障碍性增强 */
@media (prefers-reduced-motion: reduce) {
  .modern-chat-message,
  .message-container,
  .attachment-item,
  .action-btn,
  .feedback-btn {
    transition: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .chat-message-bubble {
    border-width: 2px;
  }

  .attachment-item,
  .feedback-panel {
    border-width: 2px;
  }
}
</style>
