<template>
  <svg
    class="refresh-icon"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1 4V10H7"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M23 20V14H17"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M20.49 9C19.9828 7.56678 19.1209 6.28825 17.9845 5.27455C16.8482 4.26085 15.4745 3.54933 13.9917 3.20137C12.5089 2.8534 10.9652 2.87945 9.49337 3.27684C8.02157 3.67423 6.67253 4.43346 5.57 5.48L1 10M23 14L18.43 18.52C17.3275 19.5665 15.9784 20.3258 14.5066 20.7232C13.0348 21.1205 11.4911 21.1466 10.0083 20.7986C8.52547 20.4507 7.1518 19.7392 6.01547 18.7255C4.87913 17.7117 4.01717 16.4332 3.51 15"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
</template>

<script setup lang="ts">
interface Props {
  size?: number
}

const props = withDefaults(defineProps<Props>(), {
  size: 16
})
</script>

<style scoped>
.refresh-icon {
  width: v-bind(props.size + 'px');
  height: v-bind(props.size + 'px');
}
</style>
